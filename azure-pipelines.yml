# Azure DevOps Pipeline for Pixelated Empathy - Container Apps
trigger:
  branches:
    include:
    - master
    - develop
  paths:
    include:
    - src/**
    - infra/**
    - Dockerfile.azure
    - azure-pipelines.yml
    - package.json
    - pnpm-lock.yaml

pr:
  branches:
    include:
    - master
    - develop

variables:
- name: buildConfiguration
  value: Release
- name: nodeVersion
  value: 22.x
- name: dockerTag
  value: $(Build.BuildId)
- name: azureSubscription
  value: 'pixelated-service-connection'
- name: resourceGroupName
  value: pixelated-rg
- name: appServiceName
  value: pixelated # Existing App Service with your domain
- name: containerAppName
  value: pixelated-web # Container Apps for testing
- name: containerRegistryPrefix
  value: pixelcr
- name: imageName
  value: pixelated-web
- name: azureLocation
  value: eastus
- name: environment
  value: production
- name: customDomain
  value: 'pixelatedempathy.com' # Your domain (on App Service)

stages:
- stage: Build
  displayName: Build Application
  jobs:
  - job: BuildApp
    displayName: Build Node.js Application
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    steps:
    - task: NodeTool@0
      displayName: Use Node.js $(nodeVersion)
      inputs:
        versionSpec: $(nodeVersion)

    - script: |
        npm install -g pnpm@latest
        pnpm config set store-dir $(Agent.WorkFolder)/.pnpm-store
        mkdir -p $(Agent.WorkFolder)/.pnpm-store
      displayName: Install and configure pnpm

    - task: Cache@2
      displayName: Cache pnpm dependencies
      inputs:
        key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
        restoreKeys: |
          pnpm | "$(Agent.OS)"
        path: $(Agent.WorkFolder)/.pnpm-store

    - script: |
        pnpm install --frozen-lockfile
      displayName: Install dependencies

    - script: |
        pnpm run build
      displayName: Build application

    - task: PublishBuildArtifacts@1
      displayName: Publish build artifacts
      inputs:
        PathtoPublish: 'dist'
        ArtifactName: 'dist'
        publishLocation: 'Container'

- stage: Infrastructure
  displayName: Deploy Infrastructure
  dependsOn: Build
  jobs:
  - deployment: InfrastructureDeployment
    displayName: Deploy Container Apps Infrastructure
    environment:
      name: 'azure-infrastructure'
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    strategy:
      runOnce:
        deploy:
          steps:
          - script: |
              # Try conda first, fall back to pip if conda fails
              if /home/<USER>/miniconda3/bin/conda install -c conda-forge azure-cli -y; then
                  echo "Azure CLI installed via conda"
                  echo "##vso[task.prependpath]/home/<USER>/miniconda3/bin"
                  /home/<USER>/miniconda3/bin/az version
              else
                  echo "Conda installation failed, using pip"
                  python3 -m pip install --user azure-cli==2.73.0 --break-system-packages
                  echo "##vso[task.prependpath]$HOME/.local/bin"
                  # Verify installation with full path
                  $HOME/.local/bin/az version
                  # Also verify the binary exists
                  ls -la $HOME/.local/bin/az
              fi
            displayName: Install Azure CLI 2.73.0

          - task: AzureCLI@2
            displayName: Deploy Container Apps Infrastructure
            inputs:
              azureSubscription: $(azureSubscription)
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                # Debug: Show the actual parameter values being passed
                echo "=== Deployment Parameters ==="
                echo "Resource Group: $(resourceGroupName)"
                echo "Azure Location: $(azureLocation)"
                echo "Environment: $(environment)"
                echo "============================"

                # Try primary deployment method
                chmod +x scripts/deploy-infrastructure.sh
                echo "Attempting primary deployment method..."

                if ./scripts/deploy-infrastructure.sh '$(resourceGroupName)' '$(azureLocation)' '$(environment)'; then
                    echo "✅ Primary deployment method succeeded"
                else
                    echo "❌ Primary deployment failed, trying alternative REST API method..."
                    
                    # Install jq for JSON processing
                    if ! command -v jq &> /dev/null; then
                        echo "Installing jq for JSON processing..."
                        sudo apt-get update -qq && sudo apt-get install -y jq
                    fi
                    
                    # Try REST API deployment
                    chmod +x scripts/deploy-infrastructure-rest.sh
                    if ./scripts/deploy-infrastructure-rest.sh '$(resourceGroupName)' '$(azureLocation)' '$(environment)'; then
                        echo "✅ Alternative REST API deployment succeeded"
                    else
                        echo "❌ Both standard deployment methods failed"
                        echo "Trying emergency deployment as final fallback..."
                        
                        # Emergency deployment with individual Azure CLI commands
                        chmod +x scripts/emergency-deploy.sh
                        if ./scripts/emergency-deploy.sh '$(resourceGroupName)' '$(azureLocation)' '$(environment)'; then
                            echo "✅ Emergency deployment succeeded"
                        else
                            echo "❌ All deployment methods failed including emergency fallback"
                            exit 1
                        fi
                    fi
                fi

          - task: AzureCLI@2
            displayName: Validate Infrastructure Deployment
            inputs:
              azureSubscription: $(azureSubscription)
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                # Make diagnose script non-blocking
                echo "Running infrastructure diagnostics..."
                chmod +x scripts/diagnose-containerapp.sh
                ./scripts/diagnose-containerapp.sh '$(resourceGroupName)' || {
                  echo "⚠️  Diagnostics failed, but continuing with deployment"
                  echo "This is not critical for the deployment process"
                }
            continueOnError: true

- stage: DockerBuild
  displayName: Build Docker Image
  dependsOn: Infrastructure
  jobs:
  - job: BuildDockerImage
    displayName: Build and Push Docker Image
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    steps:
    - task: DownloadBuildArtifacts@0
      displayName: Download build artifacts
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'dist'
        downloadPath: '$(System.ArtifactsDirectory)'

    - script: |
        echo "Cleaning up disk space before Docker build..."
        # Check current disk usage
        df -h /

        # Clean up Docker system
        docker system prune -af --volumes || true

        # Clean package caches
        sudo apt-get clean || true
        sudo rm -rf /var/cache/apt/archives/* || true

        # Clean temporary files
        sudo rm -rf /tmp/* || true
        sudo rm -rf /var/tmp/* || true

        # Clean old log files
        sudo find /var/log -name "*.log" -type f -mtime +7 -delete || true

        # Check disk usage after cleanup
        df -h /

        DISK_USAGE=$(df / | awk 'END{print $(NF-1)}' | sed 's/%//')
        if [ "$DISK_USAGE" -gt 90 ]; then
          echo "##[warning]Disk usage is still high: ${DISK_USAGE}%"
        else
          echo "Disk usage after cleanup: ${DISK_USAGE}%"
        fi
      displayName: Clean up disk space

    - script: |
        # Try conda first, fall back to pip if conda fails
        if /home/<USER>/miniconda3/bin/conda install -c conda-forge azure-cli -y; then
            echo "Azure CLI installed via conda"
            echo "##vso[task.prependpath]/home/<USER>/miniconda3/bin"
            /home/<USER>/miniconda3/bin/az version
        else
            echo "Conda installation failed, using pip"
            python3 -m pip install --user azure-cli==2.73.0 --break-system-packages
            echo "##vso[task.prependpath]$HOME/.local/bin"
            # Verify installation with full path
            $HOME/.local/bin/az version
            # Also verify the binary exists
            ls -la $HOME/.local/bin/az
        fi
      displayName: Install Azure CLI 2.73.0

    - task: AzureCLI@2
      displayName: Build and push Docker image
      inputs:
        azureSubscription: $(azureSubscription)
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          echo "Looking for container registry 'pixelatedcr'"

          # Use the existing pixelatedcr registry
          ACR_NAME="pixelatedcr"

          # Verify it exists, create if it doesn't
          if ! az acr show --name "$ACR_NAME" --resource-group $(resourceGroupName) &>/dev/null; then
            echo "Container registry 'pixelatedcr' not found. Creating it..."
            az acr create \
              --name "$ACR_NAME" \
              --resource-group $(resourceGroupName) \
              --location $(azureLocation) \
              --sku Standard \
              --admin-enabled true
            
            echo "Created Container Registry: $ACR_NAME"
          else
            echo "Found existing Container Registry: $ACR_NAME"
          fi

          # Login to the container registry
          az acr login --name "$ACR_NAME"

          # Get the login server
          ACR_LOGIN_SERVER=$(az acr show --name "$ACR_NAME" --resource-group $(resourceGroupName) --query loginServer --output tsv)

          echo "Container Registry Login Server: $ACR_LOGIN_SERVER"

          # Copy build artifacts to current directory
          if [ -d "$(System.ArtifactsDirectory)/dist" ]; then
            cp -r $(System.ArtifactsDirectory)/dist ./
            echo "Copied build artifacts to current directory"
          fi

          # Build and push Docker images with build cache optimization
          echo "Building Docker image with cache optimization..."
          
          # Use Dockerfile.azure if it exists, otherwise use Dockerfile
          DOCKERFILE="Dockerfile"
          if [ -f "Dockerfile.azure" ]; then
            DOCKERFILE="Dockerfile.azure"
            echo "Using Azure-specific Dockerfile: $DOCKERFILE"
          else
            echo "Using default Dockerfile: $DOCKERFILE"
          fi
          
          docker build \
            -f "$DOCKERFILE" \
            --cache-from "$ACR_LOGIN_SERVER/$(imageName):latest" \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            -t "$ACR_LOGIN_SERVER/$(imageName):$(dockerTag)" \
            -t "$ACR_LOGIN_SERVER/$(imageName):latest" \
            .

          echo "Pushing Docker images..."
          docker push "$ACR_LOGIN_SERVER/$(imageName):$(dockerTag)"
          docker push "$ACR_LOGIN_SERVER/$(imageName):latest"

          echo "Fixing App Service"
          az webapp config appsettings set \
            --name "pixelated" \
            --resource-group "pixelated-rg" \
            --settings \
              PORT=4321 \
              WEBSITES_PORT=4321 \
              NODE_ENV=production \
              WEBSITES_ENABLE_APP_SERVICE_STORAGE=false

          az webapp restart --name "pixelated" --resource-group "pixelated-rg"

          # Fix Container App (if it exists)
          az containerapp update \
            --name "pixelated-web" \
            --resource-group "pixelated-rg" \
            --set-env-vars \
              PORT=4321 \
              NODE_ENV=production

          # Verify the push was successful
          echo "Verifying image push was successful..."
          sleep 5  # Give ACR a moment to process the push

          # Check if the image exists in the registry
          IMAGE_CHECK=$(az acr repository show-tags \
            --name "$ACR_NAME" \
            --repository "$(imageName)" \
            --query "[?contains(@, '$(dockerTag)')].length(@)" \
            --output tsv 2>/dev/null || echo "0")

          if [ "$IMAGE_CHECK" = "0" ]; then
            echo "❌ Failed to verify image push. Image $(imageName):$(dockerTag) not found in registry"
            echo "Available tags:"
            az acr repository show-tags \
              --name "$ACR_NAME" \
              --repository "$(imageName)" \
              --output table 2>/dev/null || echo "No tags found"
            exit 1
          else
            echo "✅ Successfully verified image $(imageName):$(dockerTag) exists in registry"
          fi

          # Clean up local images to save space
          docker rmi "$ACR_LOGIN_SERVER/$(imageName):$(dockerTag)" || true
          docker rmi "$ACR_LOGIN_SERVER/$(imageName):latest" || true
          docker system prune -f

- stage: Deploy
  displayName: Deploy Container App
  dependsOn:
  - Build
  - Infrastructure
  - DockerBuild
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
  jobs:
  - deployment: ContainerAppDeployment
    displayName: Deploy to Container Apps
    environment:
      name: azure-production
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    strategy:
      runOnce:
        deploy:
          steps:
          - script: |
              # Try conda first, fall back to pip if conda fails
              if /home/<USER>/miniconda3/bin/conda install -c conda-forge azure-cli -y; then
                  echo "Azure CLI installed via conda"
                  echo "##vso[task.prependpath]/home/<USER>/miniconda3/bin"
                  /home/<USER>/miniconda3/bin/az version
              else
                  echo "Conda installation failed, using pip"
                  python3 -m pip install --user azure-cli==2.73.0 --break-system-packages
                  echo "##vso[task.prependpath]$HOME/.local/bin"
                  # Verify installation with full path
                  $HOME/.local/bin/az version
                  # Also verify the binary exists
                  ls -la $HOME/.local/bin/az
              fi
            displayName: Install Azure CLI 2.73.0

          - task: AzureCLI@2
            displayName: Deploy Container App
            inputs:
              azureSubscription: $(azureSubscription)
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                echo "Deploying Container App: $(containerAppName)"
                CONTAINER_APP_NAME="$(containerAppName)"

                # Check if the container app already exists
                if az containerapp show --name "$CONTAINER_APP_NAME" --resource-group $(resourceGroupName) >/dev/null 2>&1; then
                  echo "Container App exists, updating with new image..."
                  UPDATE_MODE="update"
                else
                  echo "Container App doesn't exist, creating new one..."
                  UPDATE_MODE="create"
                fi

                # Use the existing pixelatedcr registry
                ACR_NAME="pixelatedcr"

                # Verify it exists
                if ! az acr show --name "$ACR_NAME" --resource-group $(resourceGroupName) &>/dev/null; then
                  echo "❌ Container Registry 'pixelatedcr' not found in resource group"
                  echo "Available registries:"
                  az acr list --resource-group $(resourceGroupName) --query "[].name" --output table
                  exit 1
                fi

                ACR_LOGIN_SERVER=$(az acr show --name "$ACR_NAME" --resource-group $(resourceGroupName) --query loginServer --output tsv)
                echo "Using Container Registry: $ACR_NAME ($ACR_LOGIN_SERVER)"

                # Enable admin user on ACR if not already enabled
                echo "Ensuring ACR admin user is enabled..."
                az acr update --name "$ACR_NAME" --admin-enabled true

                # Get ACR admin credentials
                ACR_USERNAME=$(az acr credential show --name "$ACR_NAME" --query username --output tsv)
                ACR_PASSWORD=$(az acr credential show --name "$ACR_NAME" --query passwords[0].value --output tsv)
                echo "Retrieved ACR credentials for user: $ACR_USERNAME"

                # Verify the Docker image exists before deployment
                echo "Verifying Docker image exists: $ACR_LOGIN_SERVER/$(imageName):$(dockerTag)"
                IMAGE_EXISTS=$(az acr repository show-tags \
                  --name "$ACR_NAME" \
                  --repository "$(imageName)" \
                  --query "[?contains(@, '$(dockerTag)')].length(@)" \
                  --output tsv)

                if [ -z "$IMAGE_EXISTS" ] || [ "$IMAGE_EXISTS" = "0" ]; then
                  echo "❌ Docker image $(imageName):$(dockerTag) not found in registry"
                  echo "Available tags for $(imageName):"
                  az acr repository show-tags \
                    --name "$ACR_NAME" \
                    --repository "$(imageName)" \
                    --output table || echo "No tags found or repository doesn't exist"
                  
                  echo "Available repositories:"
                  az acr repository list \
                    --name "$ACR_NAME" \
                    --output table
                  
                  echo "Attempting to use 'latest' tag as fallback..."
                  DOCKER_IMAGE_TAG="latest"
                else
                  echo "✅ Docker image $(imageName):$(dockerTag) found in registry"
                  DOCKER_IMAGE_TAG="$(dockerTag)"
                fi

                # Find Container App Environment
                APP_ENV_NAME=$(az containerapp env list \
                  --resource-group $(resourceGroupName) \
                  --query "[?starts_with(name, 'pixel-env')].name | [0]" \
                  --output tsv)

                if [ -z "$APP_ENV_NAME" ]; then
                  echo "No Container App Environment found. Creating one..."
                  # Generate unique suffix for resources
                  UNIQUE_SUFFIX=$(echo -n "$(resourceGroupName)-$(environment)" | sha256sum | cut -c1-8)
                  APP_ENV_NAME="pixel-env-${UNIQUE_SUFFIX}"
                  
                  az containerapp env create \
                    --name "$APP_ENV_NAME" \
                    --resource-group $(resourceGroupName) \
                    --location $(azureLocation) \
                    --only-show-errors
                  
                  if [ $? -ne 0 ]; then
                    echo "❌ Failed to create Container App Environment"
                    exit 1
                  fi
                  echo "✅ Created Container App Environment: $APP_ENV_NAME"
                else
                  echo "Found Container App Environment: $APP_ENV_NAME"
                fi

                echo "Container App operation mode: $UPDATE_MODE"
                echo "Using Docker image: $ACR_LOGIN_SERVER/$(imageName):$DOCKER_IMAGE_TAG"

                if [ "$UPDATE_MODE" = "update" ]; then
                  echo "Updating existing Container App: $CONTAINER_APP_NAME"
                  
                  az containerapp update \
                    --name "$CONTAINER_APP_NAME" \
                    --resource-group $(resourceGroupName) \
                    --image "$ACR_LOGIN_SERVER/$(imageName):$DOCKER_IMAGE_TAG" \
                    --set-env-vars \
                      BUILD_ID=$(dockerTag) \
                      NODE_ENV=production \
                      PORT=4321 \
                      PUBLIC_CLERK_PUBLISHABLE_KEY="$(CLERK_PUBLISHABLE_KEY)" \
                      CLERK_SECRET_KEY="$(CLERK_SECRET_KEY)" \
                    --only-show-errors

                  if [ $? -eq 0 ]; then
                    echo "✅ Container App updated successfully"
                  else
                    echo "❌ Container App update failed"
                    exit 1
                  fi
                else
                  echo "Creating new Container App: $CONTAINER_APP_NAME"
                  
                  az containerapp create \
                    --name "$CONTAINER_APP_NAME" \
                    --resource-group $(resourceGroupName) \
                    --environment "$APP_ENV_NAME" \
                    --image "$ACR_LOGIN_SERVER/$(imageName):$DOCKER_IMAGE_TAG" \
                    --target-port 4321 \
                    --ingress 'external' \
                    --registry-server "$ACR_LOGIN_SERVER" \
                    --registry-username "$ACR_USERNAME" \
                    --registry-password "$ACR_PASSWORD" \
                    --cpu 0.5 \
                    --memory 1Gi \
                    --min-replicas 0 \
                    --max-replicas 3 \
                    --env-vars \
                      BUILD_ID=$(dockerTag) \
                      NODE_ENV=production \
                      PORT=4321 \
                      PUBLIC_CLERK_PUBLISHABLE_KEY="$(CLERK_PUBLISHABLE_KEY)" \
                      CLERK_SECRET_KEY="$(CLERK_SECRET_KEY)" \
                    --only-show-errors

                  if [ $? -eq 0 ]; then
                    echo "✅ Container App created successfully"
                  else
                    echo "❌ Container App creation failed"
                    exit 1
                  fi
                fi

                # Configure custom domain if specified
                CUSTOM_DOMAIN="$(customDomain)"
                if [ ! -z "$CUSTOM_DOMAIN" ] && [ "$CUSTOM_DOMAIN" != "" ]; then
                  echo "Configuring custom domain: $CUSTOM_DOMAIN"
                  
                  # Add custom domain to the Container App
                  az containerapp hostname add \
                    --hostname "$CUSTOM_DOMAIN" \
                    --name "$CONTAINER_APP_NAME" \
                    --resource-group $(resourceGroupName) \
                    --only-show-errors || {
                    echo "⚠️  Failed to add custom domain $CUSTOM_DOMAIN"
                    echo "Make sure:"
                    echo "1. DNS CNAME record points $CUSTOM_DOMAIN to the Container App FQDN"
                    echo "2. Domain ownership is verified"
                    echo "3. You have necessary permissions"
                  }
                  
                  # Create managed certificate for the custom domain
                  echo "Creating managed certificate for $CUSTOM_DOMAIN..."
                  az containerapp env certificate create \
                    --name "${CUSTOM_DOMAIN//./-}-cert" \
                    --environment "$APP_ENV_NAME" \
                    --resource-group $(resourceGroupName) \
                    --hostname "$CUSTOM_DOMAIN" \
                    --validation-method CNAME \
                    --only-show-errors || {
                    echo "⚠️  Failed to create managed certificate for $CUSTOM_DOMAIN"
                    echo "You may need to manually create and bind an SSL certificate"
                  }
                  
                  # Bind the certificate to the hostname
                  CERT_ID=$(az containerapp env certificate list \
                    --environment "$APP_ENV_NAME" \
                    --resource-group $(resourceGroupName) \
                    --query "[?properties.subjectName=='$CUSTOM_DOMAIN'].id | [0]" \
                    --output tsv)
                  
                  if [ ! -z "$CERT_ID" ]; then
                    echo "Binding certificate to hostname..."
                    az containerapp hostname bind \
                      --hostname "$CUSTOM_DOMAIN" \
                      --name "$CONTAINER_APP_NAME" \
                      --resource-group $(resourceGroupName) \
                      --certificate "$CERT_ID" \
                      --only-show-errors || {
                      echo "⚠️  Failed to bind certificate to $CUSTOM_DOMAIN"
                    }
                  fi
                  
                  echo "✅ Custom domain configuration completed for: $CUSTOM_DOMAIN"
                else
                  echo "No custom domain specified, using default Container App URL"
                fi

                # Get and display the Container App URL
                APP_URL=$(az containerapp show \
                  --name "$CONTAINER_APP_NAME" \
                  --resource-group $(resourceGroupName) \
                  --query properties.configuration.ingress.fqdn \
                  --output tsv)

                if [ ! -z "$APP_URL" ]; then
                  echo "🚀 Container App deployed to: https://$APP_URL"
                  echo "##vso[task.setvariable variable=containerAppUrl]https://$APP_URL"
                  
                  # Also show custom domain if configured
                  CUSTOM_DOMAIN="$(customDomain)"
                  if [ ! -z "$CUSTOM_DOMAIN" ] && [ "$CUSTOM_DOMAIN" != "" ]; then
                    echo "🌐 Custom domain: https://$CUSTOM_DOMAIN"
                  fi
                else
                  echo "⚠️  Could not retrieve Container App URL"
                fi

- stage: DeployAppService
  displayName: Deploy to App Service
  dependsOn:
  - Build
  - Infrastructure
  - DockerBuild
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
  jobs:
  - deployment: AppServiceDeployment
    displayName: Deploy to App Service
    environment:
      name: azure-production
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    strategy:
      runOnce:
        deploy:
          steps:
          - script: |
              # Try conda first, fall back to pip if conda fails
              if /home/<USER>/miniconda3/bin/conda install -c conda-forge azure-cli -y; then
                  echo "Azure CLI installed via conda"
                  echo "##vso[task.prependpath]/home/<USER>/miniconda3/bin"
                  /home/<USER>/miniconda3/bin/az version
              else
                  echo "Conda installation failed, using pip"
                  python3 -m pip install --user azure-cli==2.73.0 --break-system-packages
                  echo "##vso[task.prependpath]$HOME/.local/bin"
                  # Verify installation with full path
                  $HOME/.local/bin/az version
                  # Also verify the binary exists
                  ls -la $HOME/.local/bin/az
              fi
            displayName: Install Azure CLI 2.73.0

          - task: AzureCLI@2
            displayName: Deploy to App Service
            inputs:
              azureSubscription: $(azureSubscription)
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                echo "Deploying to App Service: $(appServiceName)"
                APP_SERVICE_NAME="$(appServiceName)"

                # Use the existing pixelatedcr registry
                ACR_NAME="pixelatedcr"
                ACR_LOGIN_SERVER=$(az acr show --name "$ACR_NAME" --resource-group $(resourceGroupName) --query loginServer --output tsv)

                echo "Using Container Registry: $ACR_NAME ($ACR_LOGIN_SERVER)"
                echo "Using Docker image: $ACR_LOGIN_SERVER/$(imageName):$(dockerTag)"

                # Get ACR credentials first
                ACR_USERNAME=$(az acr credential show --name "$ACR_NAME" --query username --output tsv)
                ACR_PASSWORD=$(az acr credential show --name "$ACR_NAME" --query passwords[0].value --output tsv)

                echo "Using ACR credentials: $ACR_USERNAME"

                # Configure App Service for container deployment using new syntax
                echo "Configuring App Service container settings..."
                az webapp config container set \
                  --name "$APP_SERVICE_NAME" \
                  --resource-group $(resourceGroupName) \
                  --container-image-name "$ACR_LOGIN_SERVER/$(imageName):$(dockerTag)" \
                  --container-registry-url "https://$ACR_LOGIN_SERVER" \
                  --container-registry-user "$ACR_USERNAME" \
                  --container-registry-password "$ACR_PASSWORD"

                # Configure App Service app settings
                echo "Configuring App Service environment variables..."
                az webapp config appsettings set \
                  --name "$APP_SERVICE_NAME" \
                  --resource-group $(resourceGroupName) \
                  --settings \
                    BUILD_ID=$(dockerTag) \
                    NODE_ENV=production \
                    PORT=4321 \
                    WEBSITES_PORT=4321 \
                    PUBLIC_CLERK_PUBLISHABLE_KEY="$(CLERK_PUBLISHABLE_KEY)" \
                    CLERK_SECRET_KEY="$(CLERK_SECRET_KEY)" \
                    WEBSITES_ENABLE_APP_SERVICE_STORAGE=false

                # Restart App Service to pick up new container
                echo "Restarting App Service to deploy new container..."
                az webapp restart \
                  --name "$APP_SERVICE_NAME" \
                  --resource-group $(resourceGroupName)

                # Get App Service URL
                APP_URL=$(az webapp show \
                  --name "$APP_SERVICE_NAME" \
                  --resource-group $(resourceGroupName) \
                  --query defaultHostName \
                  --output tsv)

                if [ ! -z "$APP_URL" ]; then
                  echo "🚀 App Service deployed to: https://$APP_URL"
                  echo "🌐 Custom domain: https://$(customDomain)"
                  echo "##vso[task.setvariable variable=appServiceUrl]https://$APP_URL"
                else
                  echo "⚠️  Could not retrieve App Service URL"
                fi

- stage: PostDeploymentTests
  displayName: Post Deployment Tests
  dependsOn:
  - Deploy
  - DeployAppService
  condition: succeeded()
  jobs:
  - job: HealthCheck
    displayName: Health Check Both Deployments
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    steps:
    - script: |
        # Try conda first, fall back to pip if conda fails
        if /home/<USER>/miniconda3/bin/conda install -c conda-forge azure-cli -y; then
            echo "Azure CLI installed via conda"
            echo "##vso[task.prependpath]/home/<USER>/miniconda3/bin"
            /home/<USER>/miniconda3/bin/az version
        else
            echo "Conda installation failed, using pip"
            python3 -m pip install --user azure-cli==2.73.0 --break-system-packages
            echo "##vso[task.prependpath]$HOME/.local/bin"
            # Verify installation with full path
            $HOME/.local/bin/az version
            # Also verify the binary exists
            ls -la $HOME/.local/bin/az
        fi
      displayName: Install Azure CLI 2.73.0

    - task: AzureCLI@2
      displayName: Health Check App Service
      inputs:
        azureSubscription: $(azureSubscription)
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          echo "=== App Service Health Check ==="
          APP_SERVICE_NAME="$(appServiceName)"

          # Get App Service URL
          APP_URL=$(az webapp show \
            --name "$APP_SERVICE_NAME" \
            --resource-group $(resourceGroupName) \
            --query defaultHostName \
            --output tsv)

          if [ -z "$APP_URL" ]; then
            echo "❌ Failed to get App Service URL"
            exit 1
          fi

          FULL_URL="https://$APP_URL"
          CUSTOM_URL="https://$(customDomain)"

          echo "Testing App Service at: $FULL_URL"
          echo "Testing custom domain at: $CUSTOM_URL"

          # Wait for deployment to complete (App Service container pulls take longer)
          echo "Waiting 90 seconds for App Service deployment to complete..."
          sleep 90

          # Health check with retries
          MAX_RETRIES=5
          RETRY_COUNT=0
          SUCCESS=false

          while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
            echo "Health check attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
            
            # Test custom domain (primary)
            HTTP_STATUS=$(curl -I "$CUSTOM_URL" --connect-timeout 10 --max-time 30 2>/dev/null | head -n1 | cut -d' ' -f2 || echo "000")
            
            if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
              echo "✅ Custom domain health check passed: $HTTP_STATUS"
              SUCCESS=true
              break
            else
              echo "⚠️  Custom domain attempt $((RETRY_COUNT + 1)) failed with status: $HTTP_STATUS"
              
              # Try default URL as fallback
              HTTP_STATUS=$(curl -I "$FULL_URL" --connect-timeout 10 --max-time 30 2>/dev/null | head -n1 | cut -d' ' -f2 || echo "000")
              if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
                echo "✅ Default URL health check passed: $HTTP_STATUS"
                SUCCESS=true
                break
              fi
            fi
            
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "Waiting 30 seconds before next attempt..."
              sleep 30
            fi
          done

          if [ "$SUCCESS" = false ]; then
            echo "❌ App Service health check failed"
            exit 1
          fi

          echo "🎉 App Service health check completed successfully!"
          echo "🌐 Live at: $CUSTOM_URL"

    - task: AzureCLI@2
      displayName: Health Check Container App
      inputs:
        azureSubscription: $(azureSubscription)
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          echo "=== Container App Health Check ==="
          CONTAINER_APP_NAME="$(containerAppName)"

          # Get Container App URL
          APP_URL=$(az containerapp show \
            --name "$CONTAINER_APP_NAME" \
            --resource-group $(resourceGroupName) \
            --query properties.configuration.ingress.fqdn \
            --output tsv)

          if [ -z "$APP_URL" ]; then
            echo "❌ Failed to get Container App URL"
            exit 1
          fi

          FULL_URL="https://$APP_URL"
          echo "Testing Container App at: $FULL_URL"

          # Health check with retries
          MAX_RETRIES=3
          RETRY_COUNT=0
          SUCCESS=false

          while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
            echo "Health check attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
            
            HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$FULL_URL" --connect-timeout 10 --max-time 30 || echo "000")
            
            if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
              echo "✅ Container App health check passed: $HTTP_STATUS"
              SUCCESS=true
              break
            else
              echo "⚠️  Container App attempt $((RETRY_COUNT + 1)) failed with status: $HTTP_STATUS"
            fi
            
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "Waiting 15 seconds before next attempt..."
              sleep 15
            fi
          done

          if [ "$SUCCESS" = false ]; then
            echo "⚠️  Container App health check failed (non-blocking)"
          else
            echo "🎉 Container App health check completed successfully!"
            echo "🧪 Testing URL: $FULL_URL"
          fi
      continueOnError: true

- stage: Cleanup
  displayName: Cleanup Old Deployments
  dependsOn: PostDeploymentTests
  condition: succeeded()
  jobs:
  - job: CleanupOldApps
    displayName: Clean Up Old Container Apps Only
    pool:
      name: 'Default'
      demands:
      - agent.name -equals decay
    steps:
    - script: |
        # Try conda first, fall back to pip if conda fails
        if /home/<USER>/miniconda3/bin/conda install -c conda-forge azure-cli -y; then
            echo "Azure CLI installed via conda"
            echo "##vso[task.prependpath]/home/<USER>/miniconda3/bin"
            /home/<USER>/miniconda3/bin/az version
        else
            echo "Conda installation failed, using pip"
            python3 -m pip install --user azure-cli==2.73.0 --break-system-packages
            echo "##vso[task.prependpath]$HOME/.local/bin"
            # Verify installation with full path
            $HOME/.local/bin/az version
            # Also verify the binary exists
            ls -la $HOME/.local/bin/az
        fi
      displayName: Install Azure CLI 2.73.0

    - task: AzureCLI@2
      displayName: Summary and Cleanup
      inputs:
        azureSubscription: $(azureSubscription)
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          echo "🎉 Dual Deployment Complete!"
          echo "================================="
          echo ""
          echo "🌐 PRODUCTION (App Service):"
          echo "   • URL: https://$(customDomain)"
          echo "   • Service: $(appServiceName)"
          echo "   • Purpose: Live production site with your domain"
          echo ""
          echo "🧪 TESTING (Container Apps):"
          CONTAINER_APP_URL=$(az containerapp show \
            --name "$(containerAppName)" \
            --resource-group $(resourceGroupName) \
            --query properties.configuration.ingress.fqdn \
            --output tsv 2>/dev/null || echo "not-deployed")
          echo "   • URL: https://$CONTAINER_APP_URL"
          echo "   • Service: $(containerAppName)"
          echo "   • Purpose: Testing new features and deployments"
          echo ""
          echo "✅ Both deployments use the same Docker image: $(dockerTag)"
          echo "✅ No DNS changes required - domain stays on App Service"
          echo ""

          # Clean up old build-specific container apps if they exist
          echo "Checking for old container apps to clean up..."
          OLD_APPS=$(az containerapp list \
            --resource-group $(resourceGroupName) \
            --query "[?starts_with(name, 'pixel-') && name != '$(containerAppName)'].name" \
            --output tsv)

          if [ ! -z "$OLD_APPS" ]; then
            echo "Found old container apps to clean up:"
            echo "$OLD_APPS"
            echo "$OLD_APPS" | while read app_name; do
              if [ ! -z "$app_name" ]; then
                echo "Deleting old Container App: $app_name"
                az containerapp delete \
                  --name "$app_name" \
                  --resource-group $(resourceGroupName) \
                  --yes \
                  --only-show-errors || {
                  echo "⚠️  Failed to delete $app_name, continuing..."
                }
              fi
            done
          else
            echo "No old container apps found to clean up"
          fi

          # Clean up old Docker images in ACR
          echo "Cleaning up old Docker images in Container Registry..."
          ACR_NAME="pixelatedcr"

          # Get all tags for the image
          ALL_TAGS=$(az acr repository show-tags \
            --name "$ACR_NAME" \
            --repository "$(imageName)" \
            --output tsv 2>/dev/null | sort -V || echo "")

          if [ ! -z "$ALL_TAGS" ]; then
            TAG_COUNT=$(echo "$ALL_TAGS" | grep -c "^" || echo "0")
            echo "Found $TAG_COUNT image tags in registry"
            
            if [ "$TAG_COUNT" -gt 5 ]; then
              TAGS_TO_DELETE=$((TAG_COUNT - 5))
              echo "Keeping only the 5 most recent image tags, deleting $TAGS_TO_DELETE oldest"
              
              echo "$ALL_TAGS" | grep -v "latest" | head -n $TAGS_TO_DELETE | while read tag; do
                if [ ! -z "$tag" ] && [ "$tag" != "$(dockerTag)" ]; then
                  echo "Deleting old image tag: $(imageName):$tag"
                  az acr repository delete \
                    --name "$ACR_NAME" \
                    --image "$(imageName):$tag" \
                    --yes \
                    --only-show-errors || {
                    echo "⚠️  Failed to delete image tag $tag, but continuing..."
                  }
                fi
              done
            else
              echo "✅ No image cleanup needed - only $TAG_COUNT tags exist"
            fi
          else
            echo "No image tags found for cleanup"
          fi

          echo "🧹 Cleanup completed!"
          echo "================================="
          echo "You can now access your production site at: https://$(customDomain)"

name: Database Backup

on:
  schedule:
    - cron: 0 */12 * * * # Run every 12 hours
  workflow_dispatch: {} # Allow manual trigger without inputs

permissions:
  contents: write

env:
  BACKUP_RETENTION_DAYS: 30
  BACKUP_DIR: ./backups

jobs:
  backup:
    name: >-
      Backup ${{ github.ref == 'refs/heads/master' && 'production' || 'staging' }} Database
    runs-on: ubuntu-latest
    environment: ${{ github.ref == 'refs/heads/master' && 'production' || 'staging' }}
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Configure Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: 1.136.3

      - name: Debug Supabase token presence
        run: |
          if [ -z "${SUPABASE_ACCESS_TOKEN}" ]; then
            echo "SUPABASE_ACCESS_TOKEN is not set"
            exit 1
          else
            echo "SUPABASE_ACCESS_TOKEN is set"
          fi
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Link Supabase project
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
        run: |
          # Create minimal supabase directory and config
          mkdir -p supabase
          cat > supabase/config.toml << EOF
          project_id = "${{ secrets.SUPABASE_PROJECT_ID }}"
          EOF

          # Link to the project using access token
          echo "Linking to project ${{ secrets.SUPABASE_PROJECT_ID }}..."
          supabase link --project-ref "${{ secrets.SUPABASE_PROJECT_ID }}"

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: 22.16.0

      - name: Enable Corepack
        run: corepack enable

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: 10.13.1
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Create backup directory
        run: mkdir -p ${{ env.BACKUP_DIR }}

      - name: Create backup
        id: backup
        env:
          PGPASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
          PGHOST: ${{ secrets.SUPABASE_DB_HOST }}
          PGUSER: ${{ secrets.SUPABASE_DB_USER }}
          PGDATABASE: ${{ secrets.SUPABASE_DB_NAME }}
          ENVIRONMENT: ${{ github.ref == 'refs/heads/master' && 'production' || 'staging' }}
        run: |
          set -euo pipefail

          # Check if all required environment variables are set
          if [ -z "$PGPASSWORD" ] || [ -z "$PGHOST" ] || [ -z "$PGUSER" ] || [ -z "$PGDATABASE" ]; then
            echo "::error::One or more required database credentials are missing"
            echo "::warning::This is likely a GitHub Secrets visibility issue. Please check repository settings."
            echo "environment=${ENVIRONMENT}" >> $GITHUB_OUTPUT
            echo "timestamp=$(date +%Y%m%d_%H%M%S)" >> $GITHUB_OUTPUT
            echo "backup_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi

          timestamp=$(date +%Y%m%d_%H%M%S)
          backup_file="supabase_backup_${ENVIRONMENT}_${timestamp}.sql"
          compressed_file="${backup_file}.gz"

          echo "::group::Creating database backup"
          # Create backup with error handling
          if ! pg_dump -p 5432 -F p > "${BACKUP_DIR}/${backup_file}"; then
            echo "::error::Failed to create database backup"
            echo "backup_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Verify backup size
          if [ ! -s "${BACKUP_DIR}/${backup_file}" ]; then
            echo "::error::Backup file is empty"
            echo "backup_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi
          echo "::endgroup::"

          echo "::group::Compressing backup"
          if ! gzip "${BACKUP_DIR}/${backup_file}"; then
            echo "::error::Failed to compress backup file"
            echo "backup_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi
          echo "::endgroup::"

          # Set outputs for next steps
          echo "environment=${ENVIRONMENT}" >> $GITHUB_OUTPUT
          echo "timestamp=${timestamp}" >> $GITHUB_OUTPUT
          echo "backup_status=success" >> $GITHUB_OUTPUT

      - name: Create configuration files backup
        id: config_backup
        env:
          ENVIRONMENT: ${{ github.ref == 'refs/heads/master' && 'production' || 'staging' }}
        run: |
          set -euo pipefail
          timestamp=$(date +%Y%m%d_%H%M%S)
          config_backup_file="config_backup_${ENVIRONMENT}_${timestamp}.tar.gz"

          echo "::group::Creating configuration files backup"
          # Create a tar.gz archive of critical configuration files
          config_files=()

          for file in "api/local.settings.json" "ai/production_config.json"; do
            if [ -f "$file" ]; then
              config_files+=("$file")
            fi
          done

          if [ ${#config_files[@]} -eq 0 ]; then
            echo "No configuration files found to backup."
            echo "config_backup_status=skipped" >> $GITHUB_OUTPUT
            exit 0
          fi

          if ! tar -czvf "${BACKUP_DIR}/${config_backup_file}" "${config_files[@]}"; then
            echo "::error::Failed to create configuration files backup"
            echo "config_backup_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Verify backup size
          if [ ! -s "${BACKUP_DIR}/${config_backup_file}" ]; then
            echo "::error::Configuration backup file is empty"
            echo "config_backup_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi
          echo "::endgroup::"
          echo "config_backup_status=success" >> $GITHUB_OUTPUT
      - name: Commit and push backup
        if: success() && steps.backup.outputs.backup_status == 'success'
        run: |
          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'
          git add ${{ env.BACKUP_DIR }}/*.sql.gz ${{ env.BACKUP_DIR }}/*.tar.gz
          git commit -m "Backup database: ${{ steps.backup.outputs.environment }} - ${{ steps.backup.outputs.timestamp }}"
          git push

      - name: Clean old backups
        if: success()
        run: |
          set -euo pipefail
          echo "::group::Cleaning old backups"
          find ${{ env.BACKUP_DIR }} -name "*.sql.gz" -type f -mtime +${{ env.BACKUP_RETENTION_DAYS }} -delete
          find ${{ env.BACKUP_DIR }} -name "*.tar.gz" -type f -mtime +${{ env.BACKUP_RETENTION_DAYS }} -delete
          echo "::endgroup::"

      - name: Notify status
        if: always()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          webhook-type: incoming-webhook
          webhook: ${{ secrets.SLACK_WEBHOOK }}
          payload: |
            {
              "text": "${{ job.status == 'success' && '✅' || '🚨' }} Database Backup Status",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Status:* ${{ job.status == 'success' && '✅ Success' || '🚨 Failure' }}"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Environment:* ${{ steps.backup.outputs.environment || 'Unknown' }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Timestamp:* ${{ steps.backup.outputs.timestamp || 'Unknown' }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Workflow:* ${{ github.workflow }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Trigger:* ${{ github.event_name }}"
                    }
                  ]
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
                  }
                }
              ]
            }

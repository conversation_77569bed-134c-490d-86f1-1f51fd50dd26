name: Monitoring

on:
  schedule:
    - cron: 0 */15 * * *
  workflow_dispatch:

permissions: read-all

env:
  NODE_VERSION: 22.16.0
  PNPM_VERSION: 10.13.1
  NODE_ENV: production

jobs:
  health-check-production:
    name: Production Health Check
    runs-on: ubuntu-latest
    # Only run for scheduled runs or when explicitly selecting production
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      - name: Get pnpm store directory
        id: get-pnpm-store-path
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.get-pnpm-store-path.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Run health checks
        id: health-check
        run: |
          echo "🏥 Running PRODUCTION health checks against ${{ vars.APP_URL }}"
          echo "ℹ️  Production failures will trigger alerts and notifications"

          # Basic endpoint check
          HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.APP_URL }})
          echo "HTTP Status: $HTTP_STATUS"

          if [[ "$HTTP_STATUS" != "200" ]]; then
            echo "::error::PRODUCTION site is down or returning errors (HTTP $HTTP_STATUS)"
            echo "status=failing" >> $GITHUB_OUTPUT
            exit 1
          fi

          # API health check (using the simple health endpoint)
          API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.APP_URL }}/api/health)
          echo "API Status: $API_STATUS"

          # Only warn but don't fail if API health check fails
          if [[ "$API_STATUS" != "200" ]]; then
            echo "::warning::API health endpoint is not responding with 200 (HTTP $API_STATUS)"
          fi

          echo "✅ PRODUCTION health checks passed"
          echo "status=passing" >> $GITHUB_OUTPUT

      - name: Create temporary config
        if: steps.health-check.outputs.status == 'passing'
        run: |
          cat > temp-playwright.config.js << 'EOF'
          export default {
            testDir: './tests/monitoring',
            use: {
              baseURL: process.env.BASE_URL || 'https://pixelatedempathy.com',
            },
            projects: [
              {
                name: 'chromium',
                use: {
                  browserName: 'chromium',
                },
              },
            ],
            reporter: ['html', 'list'],
            timeout: 60000,
            retries: 1,
          };
          EOF

      - name: Run Playwright tests
        id: run-tests
        if: steps.health-check.outputs.status == 'passing'
        run: |
          pnpm exec playwright install --with-deps chromium
          echo "Running Playwright tests against PRODUCTION: ${{ vars.APP_URL }}"

          # Run tests and store exit code to determine if any tests failed
          NODE_OPTIONS="--no-warnings --experimental-specifier-resolution=node" BASE_URL=${{ vars.APP_URL }} \
            pnpm exec playwright test --config=temp-playwright.config.js --reporter=list || true

          echo "✅ PRODUCTION monitoring tests completed"
          echo "status=success" >> $GITHUB_OUTPUT

      - name: Upload test results
        if: always() && steps.health-check.outputs.status == 'passing'
        uses: actions/upload-artifact@v4
        with:
          name: playwright-test-results-production
          path: playwright-report/
          retention-days: 30

      - name: Notify failure
        if: failure()
        uses: slackapi/slack-github-action@v1.25.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
        with:
          slack-message: |
            🚨 *PRODUCTION MONITORING FAILED*
            Environment: Production (Critical Alert)
            URL: ${{ vars.APP_URL }}
            Time: $(date)
            ${{ steps.run-tests.outputs.status == 'failure' && '⚠️ One or more monitoring tests failed. Check the GitHub Action for details.' || '' }}

            This is a PRODUCTION failure requiring immediate attention.
        continue-on-error: true

  health-check-staging:
    name: Staging Health Check
    runs-on: ubuntu-latest
    # Only run when explicitly selecting staging
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      - name: Get pnpm store directory
        id: get-pnpm-store-path
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.get-pnpm-store-path.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Run health checks
        id: health-check
        run: |
          echo "🏥 Running STAGING health checks against ${{ vars.APP_URL }}"
          echo "ℹ️  Staging failures will warn but not trigger critical alerts"

          # Basic endpoint check
          HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.APP_URL }})
          echo "HTTP Status: $HTTP_STATUS"

          if [[ "$HTTP_STATUS" != "200" ]]; then
            echo "::warning::STAGING site is down or returning errors (HTTP $HTTP_STATUS)"
            echo "::notice::This is a staging environment failure, not affecting production"
            echo "status=warning" >> $GITHUB_OUTPUT
            # Don't exit 1 for staging builds, just warn
          else
            echo "status=passing" >> $GITHUB_OUTPUT
          fi

          # API health check (using the simple health endpoint)
          API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.APP_URL }}/api/health)
          echo "API Status: $API_STATUS"

          # Only warn but don't fail if API health check fails
          if [[ "$API_STATUS" != "200" ]]; then
            echo "::warning::API health endpoint is not responding with 200 (HTTP $API_STATUS)"
          fi

          if [[ "${status}" == "warning" ]]; then
            echo "⚠️  STAGING health checks completed with warnings"
          else
            echo "✅ STAGING health checks passed"
          fi

      - name: Create temporary config
        if: steps.health-check.outputs.status == 'passing'
        run: |
          cat > temp-playwright.config.js << 'EOF'
          export default {
            testDir: './tests/monitoring',
            use: {
              baseURL: process.env.BASE_URL || 'https://pixelatedempathy.com',
            },
            projects: [
              {
                name: 'chromium',
                use: {
                  browserName: 'chromium',
                },
              },
            ],
            reporter: ['html', 'list'],
            timeout: 60000,
            retries: 1,
          };
          EOF

      - name: Run Playwright tests
        id: run-tests
        if: steps.health-check.outputs.status == 'passing'
        run: |
          pnpm exec playwright install --with-deps chromium
          echo "Running Playwright tests against STAGING: ${{ vars.APP_URL }}"

          # Run tests and store exit code to determine if any tests failed
          NODE_OPTIONS="--no-warnings --experimental-specifier-resolution=node" BASE_URL=${{ vars.APP_URL }} \
            pnpm exec playwright test --config=temp-playwright.config.js --reporter=list || true

          echo "✅ STAGING monitoring tests completed"
          echo "status=success" >> $GITHUB_OUTPUT

      - name: Upload test results
        if: always() && (steps.health-check.outputs.status == 'passing' || steps.health-check.outputs.status == 'warning')
        uses: actions/upload-artifact@v4
        with:
          name: playwright-test-results-staging
          path: playwright-report/
          retention-days: 30

      # No Slack notification for staging failures - they're non-critical

  health-check-preview:
    name: Preview Health Check
    runs-on: ubuntu-latest
    # Only run when explicitly selecting preview
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      - name: Get pnpm store directory
        id: get-pnpm-store-path
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.get-pnpm-store-path.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Run health checks
        id: health-check
        run: |
          echo "🏥 Running PREVIEW health checks against ${{ vars.APP_URL }}"
          echo "ℹ️  Preview failures will warn but not trigger alerts - this is expected for preview builds"
          echo "ℹ️  Preview sites may be temporarily unavailable during deployments or restarts"

          # Basic endpoint check
          HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.APP_URL }})
          echo "HTTP Status: $HTTP_STATUS"

          if [[ "$HTTP_STATUS" != "200" ]]; then
            echo "::warning::Preview site is down or returning errors (HTTP $HTTP_STATUS)"
            echo "::notice::This is a PREVIEW environment failure, not affecting production"
            echo "::notice::Preview deployments may be restarting or temporarily unavailable"
            echo "status=warning" >> $GITHUB_OUTPUT
            # Don't exit 1 for preview builds, just warn
          else
            echo "status=passing" >> $GITHUB_OUTPUT
          fi

          # API health check (using the simple health endpoint)
          API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${{ vars.APP_URL }}/api/health)
          echo "API Status: $API_STATUS"

          # Only warn but don't fail if API health check fails
          if [[ "$API_STATUS" != "200" ]]; then
            echo "::warning::API health endpoint is not responding with 200 (HTTP $API_STATUS)"
            echo "::notice::API may not be fully initialized in preview environment"
          fi

          if [[ "${status}" == "warning" ]]; then
            echo "⚠️  PREVIEW health checks completed with warnings (this is normal for preview builds)"
          else
            echo "✅ PREVIEW health checks passed"
          fi

      - name: Create temporary config
        if: steps.health-check.outputs.status == 'passing'
        run: |
          cat > temp-playwright.config.js << 'EOF'
          export default {
            testDir: './tests/monitoring',
            use: {
              baseURL: process.env.BASE_URL,
            },
            projects: [
              {
                name: 'chromium',
                use: {
                  browserName: 'chromium',
                },
              },
            ],
            reporter: ['html', 'list'],
            timeout: 60000,
            retries: 2, // More retries for preview builds
          };
          EOF

      - name: Run Playwright tests
        id: run-tests
        if: steps.health-check.outputs.status == 'passing'
        run: |
          pnpm exec playwright install --with-deps chromium
          echo "Running Playwright tests against PREVIEW: ${{ vars.APP_URL }}"
          echo "Note: Preview test failures are informational and do not indicate production issues"

          # Run tests and store exit code to determine if any tests failed
          NODE_OPTIONS="--no-warnings --experimental-specifier-resolution=node" BASE_URL=${{ vars.APP_URL }} \
            pnpm exec playwright test --config=temp-playwright.config.js > playwright-output.log 2>&1
          TEST_EXIT_CODE=$?

          # Display the output regardless of exit code
          cat playwright-output.log

          # Set simple success/failure indicator
          if [ $TEST_EXIT_CODE -eq 0 ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "✅ All PREVIEW tests passed successfully!"
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "::warning::Some PREVIEW tests failed - this is informational only and does not affect production"
          fi

      - name: Upload test results
        if: always() && (steps.health-check.outputs.status == 'passing' || steps.health-check.outputs.status == 'warning')
        uses: actions/upload-artifact@v4
        with:
          name: playwright-test-results-preview
          path: playwright-report/
          retention-days: 7 # Shorter retention for preview results

      # No Slack notification for preview failures - they're informational only

  check-performance:
    name: Performance Check
    runs-on: ubuntu-latest
    needs: [health-check-production]
    # Only run performance checks for production
    permissions:
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v11
        with:
          urls: |
            ${{ vars.APP_URL }}
            ${{ vars.APP_URL }}/login
            ${{ vars.APP_URL }}/dashboard
          configPath: ./.github/lighthouse-config.json
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: Check for critical performance issues
        run: |
          echo "Analyzing performance metrics for PRODUCTION..."

      - name: Notify performance issues
        if: failure()
        uses: slackapi/slack-github-action@v1.25.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
        with:
          slack-message: |
            ⚠️ *Performance Issues Detected in PRODUCTION*
            Check the Lighthouse report for details.
            Time: $(date)

            This affects production performance and should be reviewed.
        continue-on-error: true

name: Deployment Rollback

on:
  push:
    branches:
      - master
      - staging

permissions: read-all # Set restrictive default at workflow level

# Environment variables can be defined here
env:
  NODE_VERSION: 22.16.0
  PNPM_VERSION: 10.13.1
  NODE_ENV: production

jobs:
  rollback-staging:
    name: Rollback Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'
    concurrency:
      group: staging_environment
      cancel-in-progress: true
    permissions:
      contents: write
      deployments: write
      actions: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Fetch all tags
        run: git fetch --tags --force

      - name: Determine rollback tag
        id: get-tag
        run: |
          # Install dependencies for tag manager
          pnpm install --no-frozen-lockfile

          # Use the tag manager to validate and get rollback tag
          if node scripts/tag-manager.js validate staging; then
            TAGS=$(git tag -l "staging-*" --sort=-committerdate)
            TAG=$(echo "$TAGS" | sed -n '2p')
            echo "tag=$TAG" >> $GITHUB_OUTPUT
            echo "Rolling back to: $TAG"
            git checkout $TAG
          else
            echo "::warning::Cannot rollback - insufficient staging tags"
            echo "::notice::Creating initial staging tags for future rollbacks"
            node scripts/tag-manager.js create staging --message="Initial staging tag for rollback capability" --push
            echo "::error::Rollback not possible - this appears to be the first deployment"
            exit 1
          fi
        shell: /usr/bin/bash -e {0}

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Build application
        run: pnpm build

      - name: Deploy rollback to AWS
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1
        run: |
          # Deploy to AWS instead of Cloudflare
          ./scripts/deploy-aws.sh

      - name: Create rollback tag
        run: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          git tag rollback-staging-$TIMESTAMP
          git push origin rollback-staging-$TIMESTAMP

  rollback-production:
    name: Rollback Production
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    concurrency:
      group: production_environment
      cancel-in-progress: true
    permissions:
      contents: write # Required for git operations (checkout, tag, push)
      deployments: write # Required for deployment operations
      actions: read # Required for reading workflow runs
      id-token: write # Required for authentication
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Fetch all tags
        run: git fetch --tags --force

      - name: Determine rollback tag
        id: get-tag
        run: |
          # Install dependencies for tag manager
          pnpm install --no-frozen-lockfile

          # Use the tag manager to validate and get rollback tag
          if node scripts/tag-manager.js validate production; then
            TAGS=$(git tag -l "production-*" --sort=-committerdate)
            TAG=$(echo "$TAGS" | sed -n '2p')
            echo "tag=$TAG" >> $GITHUB_OUTPUT
            echo "Rolling back to: $TAG"
            git checkout $TAG
          else
            echo "::warning::Cannot rollback - insufficient production tags"
            echo "::notice::Creating initial production tags for future rollbacks"
            node scripts/tag-manager.js create production --message="Initial production tag for rollback capability" --push
            echo "::error::Rollback not possible - this appears to be the first deployment"
            exit 1
          fi
        shell: /usr/bin/bash -e {0}

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Build application
        run: pnpm build

      - name: Deploy rollback to AWS
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1
        run: |
          # Deploy to AWS instead of Cloudflare
          ./scripts/deploy-aws.sh

      - name: Create rollback tag
        run: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          git tag rollback-production-$TIMESTAMP
          git push origin rollback-production-$TIMESTAMP

name: Schedule Blog Posts

on:
  schedule:
    - cron: 0 0 * * *
  workflow_dispatch:

permissions:
  contents: write

# Environment variables can be defined here
env:
  NODE_VERSION: 22.16.0
  PNPM_VERSION: 10.13.1
  NODE_ENV: production

jobs:
  schedule-posts:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Clean pnpm cache
        run: pnpm store prune

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Run post scheduler
        run: pnpm run schedule-posts
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

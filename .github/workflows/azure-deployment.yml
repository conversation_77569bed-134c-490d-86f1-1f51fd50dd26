name: Azure Deployment

permissions:
  contents: read
  id-token: write

on:
  push:
    branches:
      - master
      - develop
  pull_request:
    branches:
      - master
  workflow_dispatch:

env:
  NODE_VERSION: 22.16.0
  PNPM_VERSION: 10.13.1
  AZURE_RESOURCE_GROUP: pixelated-rg
  AZURE_LOCATION: eastus
  AZURE_APP_NAME: pixelated

jobs:
  # Build and Test
  build:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set environment
        id: set-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "environment=production" >> $GITHUB_ENV
            echo "environment=production" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_ENV
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Enable Corepack
        run: corepack enable

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Run linting
        continue-on-error: true
        run: pnpm run lint

      - name: Run type checking
        continue-on-error: true
        run: |
          echo "Running type checking..."
          pnpm run type-check || {
            echo "❌ Type checking found issues but continuing deployment"
            exit 0
          }
          echo "✅ Type checking passed"

      - name: Run tests
        continue-on-error: true
        run: |
          echo "Running tests..."
          pnpm test || {
            echo "❌ Tests failed but continuing deployment"
            exit 0
          }
          echo "✅ Tests passed"

      - name: Build application
        run: |
          export NODE_ENV=production
          export ASTRO_CONFIG_FILE=astro.config.azure.mjs
          export AZURE_DEPLOYMENT_TYPE=server
          pnpm build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            package.json
            astro.config.azure.mjs
          retention-days: 1

  # Deploy Infrastructure
  deploy-infrastructure:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/master' || github.event_name == 'workflow_dispatch'
    # environment: azure-infrastructure

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Deploy Bicep template
        uses: azure/arm-deploy@v1
        with:
          scope: resourcegroup
          subscriptionId: ${{ vars.AZURE_SUBSCRIPTION_ID }}
          resourceGroupName: ${{ env.AZURE_RESOURCE_GROUP }}
          template: deploy/azure/main.bicep
          parameters: |
            appName=${{ env.AZURE_APP_NAME }}
            environment=${{ needs.build.outputs.environment }}
            location=${{ env.AZURE_LOCATION }}
            enableAzureOpenAI=true
            enableStorage=true
            enableMonitoring=true
            githubRepoUrl=${{ github.server_url }}/${{ github.repository }}
          failOnStdErr: false

      - name: Get deployment outputs
        id: deployment
        run: |
          OUTPUTS=$(az deployment group show \
            --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
            --name $(az deployment group list --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query '[0].name' -o tsv) \
            --query 'properties.outputs' -o json)


          echo "outputs=$OUTPUTS" >> $GITHUB_OUTPUT


          # Extract specific outputs
          APP_SERVICE_URL=$(echo "$OUTPUTS" | jq -r '.appServiceUrl.value')
          CONTAINER_REGISTRY=$(echo "$OUTPUTS" | jq -r '.containerRegistryLoginServer.value')
          KEY_VAULT_NAME=$(echo "$OUTPUTS" | jq -r '.keyVaultName.value')


          echo "app-service-url=$APP_SERVICE_URL" >> $GITHUB_OUTPUT
          echo "container-registry=$CONTAINER_REGISTRY" >> $GITHUB_OUTPUT
          echo "key-vault-name=$KEY_VAULT_NAME" >> $GITHUB_OUTPUT

      - name: Store secrets in Key Vault
        run: |
          # Store Azure OpenAI secrets
          if [[ -n "${{ secrets.AZURE_OPENAI_API_KEY }}" ]]; then
            az keyvault secret set \
              --vault-name ${{ steps.deployment.outputs.key-vault-name }} \
              --name "azure-openai-api-key" \
              --value "${{ secrets.AZURE_OPENAI_API_KEY }}"
          fi


          if [[ -n "${{ secrets.AZURE_OPENAI_ENDPOINT }}" ]]; then
            az keyvault secret set \
              --vault-name ${{ steps.deployment.outputs.key-vault-name }} \
              --name "azure-openai-endpoint" \
              --value "${{ secrets.AZURE_OPENAI_ENDPOINT }}"
          fi


          # Store Supabase secrets
          if [[ -n "${{ secrets.SUPABASE_URL }}" ]]; then
            az keyvault secret set \
              --vault-name ${{ steps.deployment.outputs.key-vault-name }} \
              --name "supabase-url" \
              --value "${{ secrets.SUPABASE_URL }}"
          fi


          if [[ -n "${{ secrets.SUPABASE_ANON_KEY }}" ]]; then
            az keyvault secret set \
              --vault-name ${{ steps.deployment.outputs.key-vault-name }} \
              --name "supabase-anon-key" \
              --value "${{ secrets.SUPABASE_ANON_KEY }}"
          fi

  # Build and Push Docker Image
  docker-build:
    runs-on: ubuntu-latest
    needs: [build, deploy-infrastructure]
    if: always() && needs.build.result == 'success'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Get container registry details
        id: acr
        run: |
          REGISTRY_NAME=$(az acr list --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query '[0].name' -o tsv)
          LOGIN_SERVER=$(az acr show --name $REGISTRY_NAME --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query 'loginServer' -o tsv)


          echo "registry-name=$REGISTRY_NAME" >> $GITHUB_OUTPUT
          echo "login-server=$LOGIN_SERVER" >> $GITHUB_OUTPUT

      - name: Login to Azure Container Registry
        run: |
          az acr login --name ${{ steps.acr.outputs.registry-name }}

      - name: Build and push Docker image
        run: |
          IMAGE_TAG="${{ needs.build.outputs.environment }}-${{ github.sha }}"


          docker build -f Dockerfile.azure -t ${{ steps.acr.outputs.login-server }}/pixelated:$IMAGE_TAG .
          docker build -f Dockerfile.azure -t ${{ steps.acr.outputs.login-server }}/pixelated:${{ needs.build.outputs.environment }} .
          docker build -f Dockerfile.azure -t ${{ steps.acr.outputs.login-server }}/pixelated:latest .


          docker push ${{ steps.acr.outputs.login-server }}/pixelated:$IMAGE_TAG
          docker push ${{ steps.acr.outputs.login-server }}/pixelated:${{ needs.build.outputs.environment }}
          docker push ${{ steps.acr.outputs.login-server }}/pixelated:latest

  # Deploy to App Service
  deploy-app-service:
    runs-on: ubuntu-latest
    needs: [build, deploy-infrastructure, docker-build]
    if: always() && needs.docker-build.result == 'success'
    environment: azure-${{ needs.build.outputs.environment }}

    steps:
      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Get deployment details
        id: details
        run: |
          APP_SERVICE_NAME=$(az webapp list --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query '[0].name' -o tsv)
          REGISTRY_NAME=$(az acr list --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query '[0].name' -o tsv)
          LOGIN_SERVER=$(az acr show --name $REGISTRY_NAME --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query 'loginServer' -o tsv)


          echo "app-service-name=$APP_SERVICE_NAME" >> $GITHUB_OUTPUT
          echo "login-server=$LOGIN_SERVER" >> $GITHUB_OUTPUT

      - name: Deploy to App Service
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ steps.details.outputs.app-service-name }}
          slot-name: ${{ needs.build.outputs.environment == 'production' && 'production' || 'staging' }}
          images: ${{ steps.details.outputs.login-server }}/pixelated:${{ needs.build.outputs.environment }}

      - name: Configure App Service settings
        run: |
          az webapp config appsettings set \
            --name ${{ steps.details.outputs.app-service-name }} \
            --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
            --slot ${{ needs.build.outputs.environment == 'production' && 'production' || 'staging' }} \
            --settings \
              NODE_ENV=${{ needs.build.outputs.environment }} \
              WEBSITES_PORT=3000 \
              WEBSITES_ENABLE_APP_SERVICE_STORAGE=false

  # Post-deployment tests
  post-deployment-tests:
    runs-on: ubuntu-latest
    needs: [build, deploy-app-service]
    if: always() && needs.deploy-app-service.result == 'success'

    steps:
      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Get App Service URL
        id: app-url
        run: |
          APP_SERVICE_NAME=$(az webapp list --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query '[0].name' -o tsv)
          SLOT_NAME=${{ needs.build.outputs.environment == 'production' && 'production' || 'staging' }}


          if [[ "$SLOT_NAME" == "production" ]]; then
            APP_URL="https://$(az webapp show --name $APP_SERVICE_NAME --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --query 'defaultHostName' -o tsv)"
          else
            APP_URL="https://$(az webapp deployment slot show --name $APP_SERVICE_NAME --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --slot $SLOT_NAME --query 'defaultHostName' -o tsv)"
          fi


          echo "app-url=$APP_URL" >> $GITHUB_OUTPUT

      - name: Health check
        run: |
          echo "Testing application health at: ${{ steps.app-url.outputs.app-url }}"


          # Wait for application to be ready
          sleep 60


          # Health check
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.app-url.outputs.app-url }}/api/health)
          if [ $response -eq 200 ]; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed with status: $response"
            exit 1
          fi


          # Basic functionality test
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.app-url.outputs.app-url }})
          if [ $response -eq 200 ]; then
            echo "✅ Application is responding"
          else
            echo "❌ Application not responding with status: $response"
            exit 1
          fi

      - name: Deployment summary
        run: |
          echo "🎉 Deployment completed successfully!"
          echo "Environment: ${{ needs.build.outputs.environment }}"
          echo "App URL: ${{ steps.app-url.outputs.app-url }}"
          echo "Resource Group: ${{ env.AZURE_RESOURCE_GROUP }}"

name: CI

permissions: read-all # Set restrictive default at workflow level

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

env:
  PNPM_HOME: ${{ github.workspace }}/.pnpm
  REDIS_URL: redis://localhost:6379
  REDIS_KEY_PREFIX: 'test:'
  VITEST_TIMEOUT: 30000
  # Skip Redis performance tests in CI to avoid flaky tests
  SKIP_REDIS_TESTS: 'true'
  # Skip FHE tests that require special libraries
  SKIP_FHE_TESTS: 'true'
  # Skip browser compatibility tests
  SKIP_BROWSER_COMPAT_TESTS: 'true'
  # Azure config for production checks
  AZURE_STORAGE_CONTAINER_NAME: 'test-container'

jobs:
  setup:
    name: Setup Versions
    runs-on: ubuntu-latest
    outputs:
      node-version: ${{ steps.set-versions.outputs.node-version }}
      pnpm-version: ${{ steps.set-versions.outputs.pnpm-version }}
    steps:
      - name: Set Node.js and pnpm versions
        id: set-versions
        run: |
          echo "node-version=22.16.0" >> $GITHUB_OUTPUT
          echo "pnpm-version=10.13.1" >> $GITHUB_OUTPUT

  lint:
    name: Lint
    runs-on: ubuntu-latest
    needs: [setup]
    permissions:
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.2
        with:
          submodules: false

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ needs.setup.outputs.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ needs.setup.outputs.pnpm-version }}
          run_install: false

      - name: Set PNPM_HOME to PATH
        run: |
          mkdir -p $PNPM_HOME
          echo "$PNPM_HOME" >> $GITHUB_PATH

      - name: Verify pnpm installation
        run: |
          which pnpm || echo "pnpm not found in PATH"
          pnpm --version
          echo "PNPM_HOME: $PNPM_HOME"
          echo "PATH: $PATH"

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Lint
        id: lint-check
        if: github.event_name == 'pull_request'
        continue-on-error: true
        run: |
          echo "Running lint check..."
          pnpm lint || {
            echo "❌ Lint check found issues but continuing workflow"
            echo "lint-status=failed" >> $GITHUB_OUTPUT
            exit 0
          }
          echo "✅ Lint check passed"
          echo "lint-status=passed" >> $GITHUB_OUTPUT

  typecheck:
    name: TypeScript Check
    runs-on: ubuntu-latest
    needs: [setup]
    permissions:
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.2
        with:
          submodules: false

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ needs.setup.outputs.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ needs.setup.outputs.pnpm-version }}
          run_install: false

      - name: Ensure Directories Exist
        run: mkdir -p /home/<USER>/work/pixelated/pixelated/node_modules/supabase/bin

      - name: Validate Environment
        run: |
          echo "Node.js version: $(node --version)"
          echo "pnpm version: $(pnpm --version)"

      - name: Set PNPM_HOME to PATH
        run: |
          mkdir -p $PNPM_HOME
          echo "$PNPM_HOME" >> $GITHUB_PATH

      - name: Verify pnpm installation
        run: |
          which pnpm || echo "pnpm not found in PATH"
          pnpm --version
          echo "PNPM_HOME: $PNPM_HOME"
          echo "PATH: $PATH"

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Type check
        id: typecheck-check
        if: github.event_name == 'pull_request'
        continue-on-error: true
        run: |
          echo "Running TypeScript type check..."
          pnpm check || {
            echo "❌ TypeScript check found issues but continuing workflow"
            echo "typecheck-status=failed" >> $GITHUB_OUTPUT
            exit 0
          }
          echo "✅ TypeScript check passed"
          echo "typecheck-status=passed" >> $GITHUB_OUTPUT

  security-audit:
    name: Dependency Vulnerability Check
    runs-on: ubuntu-latest
    needs: [setup]
    permissions:
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.2
        with:
          submodules: false

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ needs.setup.outputs.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ needs.setup.outputs.pnpm-version }}
          run_install: false

      - name: Set PNPM_HOME to PATH
        run: |
          mkdir -p $PNPM_HOME
          echo "$PNPM_HOME" >> $GITHUB_PATH

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Run PNPM Audit
        continue-on-error: true
        run: |
          echo "Running security audit..."
          pnpm audit --audit-level=high || {
            echo "❌ Security audit found vulnerabilities but continuing workflow"
            echo "Audit failures are often due to transitive dependencies that require upstream fixes"
            exit 0
          }
          echo "✅ Security audit passed"

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: [setup]
    permissions:
      contents: read
    services:
      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --name redis-service
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.2
        with:
          submodules: false

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ needs.setup.outputs.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ needs.setup.outputs.pnpm-version }}
          run_install: false

      - name: Fix Supabase CLI path issues
        run: |
          mkdir -p node_modules/supabase/bin
          chmod +x node_modules/supabase/bin

      - name: Set PNPM_HOME to PATH
        run: |
          mkdir -p $PNPM_HOME
          echo "$PNPM_HOME" >> $GITHUB_PATH

      - name: Verify pnpm installation
        run: |
          which pnpm || echo "pnpm not found in PATH"
          pnpm --version
          echo "PNPM_HOME: $PNPM_HOME"
          echo "PATH: $PATH"

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile --reporter=verbose

      - name: Install redis-cli
        run: sudo apt-get update && sudo apt-get install -y redis-tools

      - name: Verify Redis connection
        run: |
          echo "Checking Redis connection..."
          redis-cli -h localhost ping
          echo "Redis server info:"
          redis-cli -h localhost info server | grep redis_version
          echo "Testing Redis connection with CLI commands..."
          redis-cli -h localhost set test:connection-check "connected" || echo "Failed to set key"
          redis-cli -h localhost get test:connection-check || echo "Failed to get key"
          echo "Checking Redis service status..."
          sudo service redis-server status || echo "Redis service status check failed"

      - name: Verify accessibility testing dependencies
        run: |
          echo "Ensuring axe-core is installed..."
          if ! pnpm list axe-core --depth=0 | grep -q "axe-core"; then
            echo "axe-core not found, installing..."
            pnpm add -D axe-core
          else
            echo "✅ axe-core is already installed"
          fi

      - name: Rebuild Supabase binary
        run: pnpm rebuild supabase

      - name: Fix Playwright version conflicts
        run: |
          echo "Checking for multiple Playwright versions..."
          pnpm list @playwright/test --depth=0 || echo "No conflicts found in direct dependencies"
          pnpm list playwright --depth=0 || echo "No playwright package found in direct dependencies"
          # Check for any duplicate installations
          PLAYWRIGHT_VERSIONS=$(find node_modules -path "*playwright*" -type d -maxdepth 2 | sort)
          echo "Found Playwright installations:"
          echo "$PLAYWRIGHT_VERSIONS"
          # Remove any playwright packages from node_modules
          echo "Ensuring consistent Playwright version..."
          rm -rf node_modules/playwright
          # Reinstall the specific version we want
          pnpm install @playwright/test@1.52.0 --save-dev

      - name: Prepare mock directories and files
        run: |
          # Create mock directories if they don't exist
          mkdir -p src/lib/utils/__mocks__
          mkdir -p src/lib/services/redis/__mocks__
          mkdir -p src/lib/fhe/__mocks__

          # Copy basic logger mock
          echo 'export const logger = {
            info: vi.fn(),
            error: vi.fn(),
            warn: vi.fn(),
            debug: vi.fn(),
          };

          export const getLogger = vi.fn(() => logger);

          export default {
            logger,
            getLogger,
          };' > src/lib/utils/__mocks__/logger.ts

          # Ensure Redis mock exists
          if [ -f src/lib/services/redis/__mocks__/redis.mock.ts ]; then
            echo "✅ Redis mock already exists"
          else
            echo "Creating Redis mock file"
            cp -f vitest.setup.ts src/lib/services/redis/__mocks__/redis.mock.ts
          fi

          # Create FHE mocks for tests
          echo 'export const mockFHEService = {
            encrypt: vi.fn((data) => Promise.resolve(`encrypted-${data}`)),
            decrypt: vi.fn((data) => Promise.resolve(data.replace("encrypted-", ""))),
            verifySender: vi.fn(() => Promise.resolve(true)),
            processEncrypted: vi.fn(() => Promise.resolve({
              success: true,
              metadata: { operation: "test" }
            })),
          };

          export default mockFHEService;' > src/lib/fhe/__mocks__/fhe-service.ts

      - name: Install Playwright browsers
        run: pnpm exec playwright install --with-deps

      - name: Run tests with increased timeout
        run: pnpm test --pool=threads --poolOptions.threads.minThreads=0 --poolOptions.threads.maxThreads=3
        env:
          VITEST_TIMEOUT: 30000
          REDIS_URL: redis://localhost:6379
          REDIS_KEY_PREFIX: 'test:'
          NODE_ENV: test
          SKIP_REDIS_TESTS: 'true'
          SKIP_FHE_TESTS: 'true'
          # Skip performance and browser compatibility tests in CI
          SKIP_PERFORMANCE_TESTS: 'true'
          SKIP_BROWSER_COMPAT_TESTS: 'true'
          SKIP_CRYPTO_ROTATION_TEST: 'true'

      - name: Check for Playwright version conflicts
        run: |
          echo "Checking for multiple Playwright versions..."
          pnpm list @playwright/test || echo "No conflicts found in direct dependencies"
          pnpm list playwright || echo "No conflicts found in direct dependencies"
          # Look for conflicts in node_modules
          echo "Installed Playwright packages:"
          find node_modules -path "*playwright*" -type d -maxdepth 2 | sort

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ needs.setup.outputs.node-version }}

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [setup, lint, typecheck, test]
    if: |
      always() && 
      needs.setup.result == 'success' && 
      needs.test.result == 'success' &&
      (needs.lint.result == 'success' || needs.lint.result == 'failure') &&
      (needs.typecheck.result == 'success' || needs.typecheck.result == 'failure')
    permissions:
      contents: read
      actions: read
    steps:
      - name: Check lint and typecheck status
        run: |
          echo "## Code Quality Status" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Lint | ${{ needs.lint.result == 'success' && '✅ Passed' || '❌ Failed (non-blocking)' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| TypeCheck | ${{ needs.typecheck.result == 'success' && '✅ Passed' || '❌ Failed (non-blocking)' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Tests | ${{ needs.test.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo ""
          echo "🚀 Continuing with build despite code quality issues..."

      - name: Checkout repository
        uses: actions/checkout@v4.2.2
        with:
          submodules: false

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ needs.setup.outputs.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ needs.setup.outputs.pnpm-version }}
          run_install: false

      - name: Set PNPM_HOME to PATH
        run: |
          mkdir -p $PNPM_HOME
          echo "$PNPM_HOME" >> $GITHUB_PATH

      - name: Verify pnpm installation
        run: |
          which pnpm || echo "pnpm not found in PATH"
          pnpm --version
          echo "PNPM_HOME: $PNPM_HOME"
          echo "PATH: $PATH"

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile --reporter=verbose

      - name: Fix Playwright version conflicts
        run: |
          echo "Checking for multiple Playwright versions..."
          pnpm list @playwright/test --depth=0 || echo "No conflicts found in direct dependencies"
          pnpm list playwright --depth=0 || echo "No playwright package found in direct dependencies"
          # Check for any duplicate installations
          PLAYWRIGHT_VERSIONS=$(find node_modules -path "*playwright*" -type d -maxdepth 2 | sort)
          echo "Found Playwright installations:"
          echo "$PLAYWRIGHT_VERSIONS"
          # Remove any playwright packages from node_modules
          echo "Ensuring consistent Playwright version..."
          rm -rf node_modules/playwright
          # Reinstall the specific version we want
          pnpm install @playwright/test@1.52.0 --save-dev

      - name: Rebuild Supabase binary
        run: pnpm rebuild supabase

      - name: Ensure UI component directories exist
        run: |
          mkdir -p src/components/ui/button

          echo 'export { Button, type ButtonProps, type ButtonVariant, type ButtonSize } from "../button"' \
            > src/components/ui/button/index.ts
          echo 'export { default } from "../button"' >> src/components/ui/button/index.ts

      - name: Build
        run: pnpm build
        env:
          SKIP_REDIS_TESTS: 'true'
          SKIP_FHE_TESTS: 'true'
          REDIS_URL: redis://localhost:6379
          REDIS_KEY_PREFIX: 'test:'
          SKIP_BROWSER_COMPAT_TESTS: 'true'

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: dist
          retention-days: 7

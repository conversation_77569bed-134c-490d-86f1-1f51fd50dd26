name: Deploy to AWS Amplify

# Set default restrictive permissions (avoid write-all)
permissions: {}

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

env:
  NODE_VERSION: 22.16.0
  PNPM_VERSION: 10.13.1
  NODE_ENV: production
  AWS_DEPLOYMENT: '1'

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false # avoid duplicate install

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4.2.3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Lint and type check
        run: |
          pnpm oxc
          pnpm type-check > ts_errors.log 2>&1

      - name: Run tests
        run: pnpm test

      - name: Build for AWS
        run: |
          echo "Building with AWS configuration..."
          pnpm build
        env:
          AWS_DEPLOYMENT: '1'
          NODE_ENV: production

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy to Amplify
        run: |
          chmod +x scripts/deploy-amplify.sh
          ./scripts/deploy-amplify.sh
        env:
          AMPLIFY_APP_ID: ${{ secrets.AMPLIFY_APP_ID }}
          AWS_REGION: ${{ secrets.AWS_REGION }}

      - name: Notify deployment status
        if: always()
        run: |
          if [ ${{ job.status }} == 'success' ]; then
            echo "✅ Deployment to AWS Amplify successful!"
          else
            echo "❌ Deployment to AWS Amplify failed!"
          fi

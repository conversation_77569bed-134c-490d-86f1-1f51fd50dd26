{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.36.177.2456", "templateHash": "8557656098764970963"}}, "parameters": {"environmentName": {"type": "string", "metadata": {"description": "Environment name (e.g., production, staging, development)"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Azure region for resource deployment"}}, "resourcePrefix": {"type": "string", "defaultValue": "pixel", "metadata": {"description": "Prefix for resource names"}}, "createdDate": {"type": "string", "defaultValue": "[utcNow('yyyy-MM-dd')]", "metadata": {"description": "Current timestamp for resource creation"}}}, "variables": {"resourceToken": "[uniqueString(subscription().id, resourceGroup().id, parameters('environmentName'))]", "commonTags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2015-11-01-preview", "name": "[format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken'))]", "location": "[parameters('location')]", "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}, "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": 30, "workspaceCapping": {"dailyQuotaGb": 1}}}, {"type": "Microsoft.Insights/components", "apiVersion": "2020-02-02", "name": "[format('{0}-ai-{1}', parameters('resourcePrefix'), variables('resourceToken'))]", "location": "[parameters('location')]", "kind": "web", "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}, "properties": {"Application_Type": "web", "WorkspaceResourceId": "[resourceId('Microsoft.OperationalInsights/workspaces', format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken')))]", "DisableIpMasking": false, "DisableLocalAuth": false}, "dependsOn": ["[resourceId('Microsoft.OperationalInsights/workspaces', format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken')))]"]}, {"type": "Microsoft.KeyVault/vaults", "apiVersion": "2015-06-01", "name": "[format('{0}-kv-{1}', parameters('resourcePrefix'), variables('resourceToken'))]", "location": "[parameters('location')]", "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}, "properties": {"tenantId": "[subscription().tenantId]", "sku": {"family": "A", "name": "standard"}, "accessPolicies": [], "enabledForDeployment": true, "enabledForTemplateDeployment": true, "enableSoftDelete": true, "enablePurgeProtection": true, "softDeleteRetentionInDays": 7, "networkAcls": {"bypass": "AzureServices", "defaultAction": "Allow"}}}, {"type": "Microsoft.ContainerRegistry/registries", "apiVersion": "2017-10-01", "name": "pixelatedcr", "location": "[parameters('location')]", "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}, "sku": {"name": "Standard"}, "properties": {"adminUserEnabled": false, "publicNetworkAccess": "Enabled", "dataEndpointEnabled": false}}, {"type": "Microsoft.App/managedEnvironments", "apiVersion": "2022-01-01-preview", "name": "[format('{0}-env-{1}', parameters('resourcePrefix'), variables('resourceToken'))]", "location": "[parameters('location')]", "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}, "properties": {"appLogsConfiguration": {"destination": "log-analytics", "logAnalyticsConfiguration": {"customerId": "[reference(resourceId('Microsoft.OperationalInsights/workspaces', format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken'))), '2015-11-01-preview').customerId]", "sharedKey": "[listKeys(resourceId('Microsoft.OperationalInsights/workspaces', format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken'))), '2015-11-01-preview').primarySharedKey]"}}, "zoneRedundant": false}, "dependsOn": ["[resourceId('Microsoft.OperationalInsights/workspaces', format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken')))]"]}, {"type": "Microsoft.ManagedIdentity/userAssignedIdentities", "apiVersion": "2015-08-31-preview", "name": "[format('{0}-uid', parameters('resourcePrefix'))]", "location": "[parameters('location')]", "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]"}}, {"type": "Microsoft.App/containerApps", "apiVersion": "2022-01-01-preview", "name": "[format('{0}-{1}', parameters('resourcePrefix'), variables('resourceToken'))]", "location": "[parameters('location')]", "identity": {"type": "UserAssigned", "userAssignedIdentities": {"[format('{0}', resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', format('{0}-uid', parameters('resourcePrefix'))))]": {}}}, "tags": {"Environment": "[parameters('environmentName')]", "Project": "PixelatedEmpathy", "ManagedBy": "AzureDevOps", "CreatedDate": "[parameters('createdDate')]", "azd-service-name": "[parameters('resourcePrefix')]", "azd-env-name": "[parameters('environmentName')]"}, "properties": {"managedEnvironmentId": "[resourceId('Microsoft.App/managedEnvironments', format('{0}-env-{1}', parameters('resourcePrefix'), variables('resourceToken')))]", "configuration": {"registries": [{"server": "[reference(resourceId('Microsoft.ContainerRegistry/registries', 'pixelatedcr'), '2017-10-01').loginServer]", "identity": "[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', format('{0}-uid', parameters('resourcePrefix')))]"}], "secrets": [], "ingress": {"external": true, "targetPort": 4321, "transport": "auto", "allowInsecure": false, "corsPolicy": {"allowedOrigins": ["*"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["*"], "exposeHeaders": ["*"], "maxAge": 86400}}}, "template": {"containers": [{"name": "[parameters('resourcePrefix')]", "image": "[format('{0}/{1}:latest', reference(resourceId('Microsoft.ContainerRegistry/registries', 'pixelatedcr'), '2017-10-01').loginServer, parameters('resourcePrefix'))]", "resources": {"cpu": "[json('1.0')]", "memory": "2Gi"}, "env": [{"name": "ENVIRONMENT", "value": "[parameters('environmentName')]"}, {"name": "PORT", "value": "4321"}]}], "scale": {"minReplicas": 1, "maxReplicas": 3, "rules": [{"name": "http-scaling", "http": {"metadata": {"concurrentRequests": "30"}}}]}}}, "dependsOn": ["[resourceId('Microsoft.App/managedEnvironments', format('{0}-env-{1}', parameters('resourcePrefix'), variables('resourceToken')))]", "[resourceId('Microsoft.ContainerRegistry/registries', 'pixelatedcr')]", "[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', format('{0}-uid', parameters('resourcePrefix')))]"]}], "outputs": {"containerAppUrl": {"type": "string", "value": "[reference(resourceId('Microsoft.App/containerApps', format('{0}-{1}', parameters('resourcePrefix'), variables('resourceToken'))), '2022-01-01-preview').configuration.ingress.fqdn]"}, "RESOURCE_GROUP_ID": {"type": "string", "value": "[resourceGroup().id]"}, "AZURE_CONTAINER_REGISTRY_ENDPOINT": {"type": "string", "value": "[reference(resourceId('Microsoft.ContainerRegistry/registries', 'pixelatedcr'), '2017-10-01').loginServer]"}, "keyVaultName": {"type": "string", "value": "[format('{0}-kv-{1}', parameters('resourcePrefix'), variables('resourceToken'))]"}, "appInsightsName": {"type": "string", "value": "[format('{0}-ai-{1}', parameters('resourcePrefix'), variables('resourceToken'))]"}, "logAnalyticsName": {"type": "string", "value": "[format('{0}-log-{1}', parameters('resourcePrefix'), variables('resourceToken'))]"}, "containerRegistryName": {"type": "string", "value": "pixelatedcr"}}}
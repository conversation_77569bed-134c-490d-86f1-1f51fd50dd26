{"name": "pixelated", "type": "module", "version": "0.0.1", "private": true, "packageManager": "pnpm@10.13.1", "license": "MIT", "engines": {"node": "22"}, "scripts": {"astro": "astro", "sync": "astro sync", "dev": "astro dev", "start": "astro preview --host 0.0.0.0 --port 3000", "build": "astro build", "build:vercel": "NODE_OPTIONS='--max-old-space-size=8192' ASTRO_CONFIG_FILE=astro.config.vercel.mjs astro build", "preview": "astro preview", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx,astro,json}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,md,mdx,astro,json}\"", "lint": "oxlint src", "lint:ci": "oxlint src || exit 0", "lint:fix": "oxlint --fix src", "typecheck": "astro check && tsc --noEmit", "typecheck:aws": "tsc --noEmit --project tsconfig.aws.json", "typecheck:strict": "tsx scripts/validate-typescript.ts", "type-check": "pnpm run typecheck", "check": "astro check", "benchmark": "node scripts/benchmark.js", "supabase:types": "supabase gen types typescript --linked > src/types/supabase.ts", "redis:check": "node src/lib/services/redis/health-check.js", "memory:test": "node src/lib/memory/memory-test.js", "ai:test": "node src/lib/ai/test-ai-services.js", "fhe:test": "node src/lib/fhe/test-fhe.js", "backup:test": "node src/lib/backup/backup-test.js", "e2e": "playwright test", "e2e:smoke": "playwright test tests/e2e/smoke/", "e2e:browser": "playwright test tests/browser/", "e2e:ui": "playwright test --ui-mode", "security:scan": "bash ./scripts/security-scan.sh", "performance:test": "node ./tests/performance/performance-test.js", "docker:build": "docker build -t pixelated .", "docker:run": "docker run -p 3000:3000 pixelated", "check:all": "pnpm run format:check && pnpm run lint && pnpm run typecheck", "toolbar:on": "astro preferences enable devToolbar", "toolbar:off": "astro preferences disable dev<PERSON><PERSON><PERSON>", "trunk": "trunk", "fmt": "trunk fmt", "merge-datasets:install": "bash ./scripts/install-dataset-deps.sh", "merge-datasets": "tsx src/lib/ai/datasets/cli.ts", "prepare-openai": "tsx -e \"import { prepareForOpenAI } from './src/lib/ai/datasets/prepare-fine-tuning'; prepareForOpenAI();\"", "prepare-huggingface": "tsx -e \"import { prepareForHuggingFace } from './src/lib/ai/datasets/prepare-fine-tuning'; prepareForHuggingFace();\"", "prepare-all-formats": "tsx -e \"import { prepareAllFormats } from './src/lib/ai/datasets/prepare-fine-tuning'; prepareAllFormats();\"", "security:check": "node scripts/clean-credentials.js --check-only", "security:fix": "node scripts/clean-credentials.js", "security:sanitize-logs": "node scripts/sanitize-build-logs.js", "generate-dialogues": "node src/scripts/generate_dialogues.js", "batch-generate-dialogues": "node src/scripts/batch_generate_dialogues.js", "validate-dialogues": "node src/scripts/validate_dialogues.js", "dialogue-pipeline": "node src/scripts/run_full_dialogue_pipeline.js", "blog-publisher": "tsx src/lib/scripts/blog-publisher.ts", "blog": "node scripts/blog-web.js", "schedule-posts": "node scripts/schedule-posts.js", "deploy": "node scripts/consolidated-deploy.js deploy staging", "deploy:prod": "node scripts/consolidated-deploy.js deploy production", "deploy:enhanced": "node scripts/enhanced-deploy.js deploy staging", "deploy:enhanced:prod": "node scripts/enhanced-deploy.js deploy production", "rollback": "node scripts/enhanced-deploy.js rollback staging", "rollback:prod": "node scripts/enhanced-deploy.js rollback production", "deploy:vercel": "bash scripts/deploy-vercel.sh", "deploy:vercel:prod": "VERCEL_ENV=production bash scripts/deploy-vercel.sh", "tags:create": "node scripts/tag-manager.js create", "tags:list": "node scripts/tag-manager.js list", "tags:validate": "node scripts/tag-manager.js validate", "tags:cleanup": "node scripts/tag-manager.js cleanup", "tags:maintenance": "node scripts/tag-maintenance.js full-maintenance", "prismjs": "1.30.0", "version:release": "node scripts/version-manager.js release", "version:info": "node scripts/version-manager.js info", "test": "vitest", "test:unit": "vitest run --coverage", "test:integration": "vitest run tests/integration/", "test:e2e": "playwright test", "test:e2e-ui": "playwright test --ui", "test:smoke": "playwright test tests/e2e/smoke/", "test:coverage": "vitest run --coverage", "test:watch": "vitest watch", "test:all": "node scripts/consolidated-test.js all", "test:hipaa": "node scripts/consolidated-test.js hipaa", "test:crypto": "node scripts/consolidated-test.js crypto", "test:backup": "node scripts/consolidated-test.js backup", "test:security": "node scripts/consolidated-test.js security", "test:bias-detection": "vitest run src/lib/ai/bias-detection/ src/components/admin/bias-detection/", "test:performance": "k6 run src/load-tests/bias-detection-load-test.js", "initialize-models": "tsx src/scripts/initialize-cognitive-models.ts", "mcp:install": "pnpm add -D @smithery/cli @21st-dev/magic cursor-mcp-installer-free", "mcp:perplexity": "pnpm exec @smithery/cli run @arjunkmrm/perplexity-search --key ab351ddc-26d1-4448-a316-d9ad9c5df416", "mcp:magic": "pnpm exec @21st-dev/magic", "mcp:github": "pnpm exec @smithery/cli run @smithery-ai/github --key ab351ddc-26d1-4448-a316-d9ad9c5df416", "mcp:thinking": "pnpm exec @smithery/cli run @smithery-ai/server-sequential-thinking --key ab351ddc-26d1-4448-a316-d9ad9c5df416", "mcp:gitingest": "pnpm exec @smithery/cli run @puravparab/gitingest-mcp --key 2027c5a9-0f31-4895-80c9-82bbfddb4287", "mcp:think": "pnpm exec @smithery/cli run @PhillipRt/think-mcp-server --key ab351ddc-26d1-4448-a316-d9ad9c5df416", "mcp:playwright": "pnpm exec @smithery/cli run @microsoft/playwright-mcp --key 2027c5a9-0f31-4895-80c9-82bbfddb4287", "mcp:time": "pnpm exec @smithery/cli run @yokingma/time-mcp --key 2027c5a9-0f31-4895-80c9-82bbfddb4287", "dev:bias-detection": "tsx src/lib/ai/bias-detection/server.ts", "dev:ai-service": "tsx src/lib/ai/services/server.ts", "dev:analytics": "tsx src/lib/analytics/server.ts", "dev:worker": "tsx src/lib/jobs/worker.ts", "dev:all-services": "concurrently \"pnpm dev\" \"pnpm dev:bias-detection\" \"pnpm dev:ai-service\" \"pnpm dev:analytics\" \"pnpm dev:worker\"", "docker:up": "./scripts/deploy.sh", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "docker:reset": "./scripts/reset-dev.sh", "setup:dev": "./scripts/setup-dev.sh", "mcp:installer": "pnpm exec cursor-mcp-installer-free /workspaces/pixelated", "mcp:list": "node scripts/mcp-manager.js list", "mcp:run": "node scripts/mcp-manager.js run", "mcp:manage": "node scripts/mcp-manager.js", "ts:debug": "./debug-typescript.sh normal", "ts:debug:verbose": "./debug-typescript.sh verbose", "ts:debug:minimal": "./debug-typescript.sh minimal", "ts:debug:clean": "./debug-typescript.sh clean", "ts:debug:repro": "./debug-typescript.sh repro", "ts:debug:monitor": "./debug-typescript.sh monitor", "ts:check:debug": "tsc --noEmit --project tsconfig.debug.json", "ts:check:main": "tsc --noEmit --project tsconfig.json", "ts:check:aws": "tsc --noEmit --project tsconfig.aws.json", "ts:compare": "pnpm run ts:check:debug && echo '✅ Debug config works' && pnpm run ts:check:main && echo '✅ Main config works'", "ts:switch:debug": "cp tsconfig.json tsconfig.json.backup && cp tsconfig.debug.json tsconfig.json", "ts:switch:main": "cp tsconfig.json.backup tsconfig.json", "ts:logs": "echo 'TypeScript logs location varies by system. Run: ./debug-typescript.sh normal'"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@astrojs/markdoc": "^0.15.1", "@astrojs/mdx": "^4.3.1", "@astrojs/node": "^9.3.0", "@astrojs/react": "^4.3.0", "@aws-amplify/cli": "^14.0.0", "@aws-sdk/client-dynamodb": "^3.848.0", "@aws-sdk/client-kms": "^3.848.0", "@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/lib-dynamodb": "^3.848.0", "@aws-sdk/util-dynamodb": "^3.848.0", "@axe-core/react": "^4.10.2", "@clerk/astro": "^2.10.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@google/genai": "^1.10.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@iconify-json/lucide": "^1.2.57", "@iconify/utils": "2.2.0", "@libsql/client": "^0.15.10", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^10.5.2", "@react-three/fiber": "^9.2.0", "@sentry/astro": "^9.40.0", "@sentry/browser": "^9.40.0", "@sentry/cli": "^2.50.2", "@sentry/profiling-node": "^9.40.0", "@sentry/vite-plugin": "^3.6.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tailwindcss/vite": "^4.1.11", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-layers": "^4.22.0", "@types/ws": "^8.18.1", "astro-compress": "^2.3.8", "astro-icon": "^1.1.5", "astro-seo": "^0.8.4", "aws-amplify": "^6.15.3", "axios": "^1.11.0", "buffer": "^6.0.3", "chart.js": "^4.5.0", "circomlib": "^2.0.5", "clsx": "^2.1.1", "commander": "^13.1.0", "crypto-js": "^4.2.0", "date-fns": "4.1.0", "fast-glob": "^3.3.3", "flexsearch": "^0.8.205", "framer-motion": "^12.23.6", "gray-matter": "^4.0.3", "ioredis": "^5.6.1", "jotai": "^2.12.5", "katex": "^0.16.22", "lucide-react": "^0.514.0", "mermaid": "^11.9.0", "nanoid": "^5.1.5", "node-cron": "^4.2.1", "node-seal": "^5.1.6", "nprogress": "^0.2.0", "p5": "^2.0.3", "pg": "8.16.0", "postgres": "^3.4.7", "preact": "^10.26.9", "promise-polyfill": "^8.3.0", "prop-types": "^15.8.1", "rate-limiter-flexible": "^7.1.1", "react-chartjs-2": "^5.3.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "react-router-dom": "^7.7.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.1.0", "redis": "5.1.0", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "resend": "^4.7.0", "sanitize-html": "^2.17.0", "simple-git": "^3.28.0", "sonner": "^2.0.6", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "three": "^0.178.0", "three-stdlib": "^2.36.0", "twilio": "^5.7.3", "typescript-eslint": "^8.38.0", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "web-push": "^3.6.7", "whatwg-fetch": "^3.6.20", "ws": "^8.18.3", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6", "zustand": "^5.0.6"}, "devDependencies": {"@21st-extension/toolbar": "^0.5.14", "@antfu/eslint-config": "^4.18.0", "@ascorbic/feed-loader": "^1.0.4", "@astrojs/check": "^0.9.4", "@astrojs/language-server": "^2.15.4", "@astrojs/markdown-remark": "^6.3.3", "@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.4.1", "@astrojs/ts-plugin": "^1.10.4", "@aws-sdk/credential-provider-ini": "^3.848.0", "@axe-core/playwright": "^4.10.2", "@capacitor/preferences": "^7.0.1", "@eslint/css": "^0.9.0", "@eslint/js": "^9.31.0", "@eslint/json": "^0.12.0", "@eslint/markdown": "^6.6.0", "@eslint/plugin-kit": "0.3.3", "@expressive-code/plugin-collapsible-sections": "^0.41.3", "@expressive-code/plugin-line-numbers": "^0.41.3", "@fontsource/dm-mono": "^5.2.6", "@fontsource/inter": "^5.2.6", "@fontsource/roboto-condensed": "^5.2.6", "@iconify-json/bx": "^1.2.2", "@iconify-json/carbon": "^1.2.11", "@iconify-json/fa-solid": "^1.2.1", "@iconify-json/grommet-icons": "^1.2.1", "@iconify-json/ri": "^1.2.5", "@iconify-json/uil": "^1.2.3", "@iconify/json": "^2.2.360", "@keystatic/astro": "^5.0.6", "@playwright/test": "^1.54.1", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "14.6.1", "@trunkio/launcher": "^1.3.4", "@types/archiver": "^6.0.3", "@types/k6": "^1.1.1", "@types/mdast": "^4.0.4", "@types/node": "^24.1.0", "@types/node-fetch": "^2.6.12", "@types/nprogress": "^0.2.3", "@types/p5": "^1.7.6", "@types/papaparse": "^5.3.16", "@types/pdfkit": "^0.14.0", "@types/pg": "8.15.2", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/sanitize-html": "^2.16.0", "@types/three": "^0.177.0", "@types/uuid": "^10.0.0", "@types/web-push": "^3.6.4", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@unocss/astro": "^66.3.3", "@unocss/core": "^66.3.3", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@unocss/preset-web-fonts": "^66.3.3", "@unocss/preset-wind3": "^66.3.3", "@unocss/reset": "^0.63.6", "@unocss/transformer-directives": "^66.3.3", "@unocss/transformer-variant-group": "^66.3.3", "@unocss/vite": "^66.3.3", "@upstash/context7-mcp": "^1.0.14", "@vercel/kv": "^1.0.1", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue/runtime-core": "^3.5.18", "@webcomponents/custom-elements": "^1.6.0", "@zemd/eslint-astro": "^0.0.8", "archiver": "^7.0.1", "astro": "^5.12.2", "astro-eslint-parser": "^1.2.2", "astro-expressive-code": "^0.41.3", "astro-loader-github-prs": "^1.2.1", "astro-loader-github-releases": "^2.0.2", "astro-robots-txt": "^1.0.0", "astro-vitesse": "^0.5.2", "autoprefixer": "^10.4.21", "aws-cdk-lib": "2.204.0", "axe-core": "^4.10.3", "braces": "^3.0.3", "chalk": "^5.4.1", "cheerio": "^1.1.2", "class-variance-authority": "^0.7.1", "concurrently": "^9.2.0", "constructs": "^10.4.2", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "del-cli": "^6.0.0", "dotenv": "^16.6.1", "esbuild": "^0.25.8", "eslint": "^9.31.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-vitest": "^0.5.4", "eslint-plugin-vitest-globals": "^1.5.0", "espree": "^10.4.0", "glob": "^10.4.5", "glob-parent": "^6.0.2", "globals": "^16.3.0", "globby": "^14.1.0", "hastscript": "^9.0.1", "html-entities": "^2.6.0", "intersection-observer": "^0.12.2", "js-base64": "^3.7.7", "jsdom": "^22.1.0", "lint-staged": "^16.1.2", "madge": "^8.0.0", "mdast-util-directive": "^3.1.0", "mdast-util-to-string": "^4.0.0", "medium-zoom": "^1.1.0", "micromatch": "^4.0.8", "msw": "^2.10.4", "oxc-parser": "^0.77.3", "oxlint": "^1.8.0", "papaparse": "^5.5.3", "path-browserify": "^1.0.1", "path-to-regexp": "8.2.0", "picomatch": "^4.0.3", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "postinstall-postinstall": "^2.1.0", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prismjs": "1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "reading-time": "^1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-callouts": "^2.1.2", "rehype-external-links": "^3.0.0", "rehype-katex": "^7.0.1", "remark-code-import": "^1.2.0", "remark-directive": "^4.0.0", "remark-imgattr": "^1.0.5", "remark-math": "^6.0.0", "remark-toc": "^9.0.0", "resize-observer-polyfill": "^1.5.1", "rollup-plugin-visualizer": "^6.0.3", "satori": "^0.15.2", "satori-html": "^0.3.2", "scheduler": "^0.26.0", "shiki": "^3.8.1", "stream-browserify": "^3.0.0", "supabase": "^2.31.8", "terser": "^5.43.1", "ts-node": "^10.9.2", "tsconfig": "^7.0.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "unocss": "^66.3.3", "url-polyfill": "^1.1.13", "util": "^0.12.5", "vfile": "^6.0.3", "vite": "^7.0.5", "vite-plugin-oxlint": "^1.4.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vue": "^3.5.18", "wait-on": "^8.0.3", "web-animations-js": "^2.3.2"}, "allowedDeprecatedVersions": {"abab": "2.0.6", "domexception": "4.0.0", "lodash.get": "4.4.2", "@npmcli/move-file": "3.0.0", "are-we-there-yet": "4.0.2", "gauge": "5.0.2", "node-domexception": "2.0.2", "npmlog": "7.0.1", "querystring": "0.2.1"}, "pnpm": {"overrides": {"path-to-regexp": "8.2.0", "@vitest/coverage-v8>glob": "^10.4.5", "glob": "^10.4.5", "rimraf": "^5.0.0", "inflight": "^2.0.0", "@humanwhocodes/config-array": "^1.0.0", "@humanwhocodes/object-schema": "^2.0.0", "abab": "^2.0.6", "domexception": "^4.0.0", "lodash.get": "^4.4.2", "node-domexception": "^2.0.0", "npmlog": "^7.0.0", "@npmcli/move-file": "^3.0.0", "are-we-there-yet": "^4.0.0", "gauge": "^5.0.0", "querystring": "^0.2.1", "undici@<5.29.0": ">=5.29.0", "tar-fs@>=2.0.0 <2.1.3": ">=2.1.3"}, "allowedDeprecatedVersions": {"abab": "2.0.6", "domexception": "4.0.0", "lodash.get": "4.4.2", "@npmcli/move-file": "3.0.0", "are-we-there-yet": "4.0.2", "gauge": "5.0.2", "node-domexception": "2.0.2", "npmlog": "7.0.1", "querystring": "0.2.1"}, "peerDependencyRules": {"allowAny": ["eslint", "unocss"]}, "onlyBuiltDependencies": ["@aws-amplify/cli", "@clerk/shared", "@contrast/fn-inspect", "@newrelic/native-metrics", "@parcel/watcher", "@sentry-internal/node-cpu-profiler", "@sentry/cli", "@tailwindcss/oxide", "aws-sdk", "bufferutil", "core-js", "deno", "esbuild", "libpg-query", "msw", "onnxruntime-node", "postinstall-postinstall", "protobufjs", "sharp", "sqlite3", "supabase", "unrs-resolver", "workerd"], "ignoredBuiltDependencies": ["libpg-query"]}, "*.{json,md,yml,yaml}": ["prettier --write"], "resolutions": {"path-to-regexp": "8.2.0"}, "trustedDependencies": ["@clerk/shared", "aws-sdk", "core-js", "onnxruntime-node", "postinstall-postinstall", "protobufjs", "unrs-resolver"]}
---
import { Image } from 'astro:assets'
import type { ImageMetadata } from 'astro'

export interface Props {
  images: {
    src: ImageMetadata | string
    alt: string
  }[]
  imageProps?: {
    width?: number
    height?: number
    quality?: number
  }
  class?: string
}

const { images, imageProps = {}, class: className } = Astro.props
---

<swiper-container
  style="--swiper-pagination-color: #374151"
  class={className}
  pagination="true"
  pagination-clickable="true"
  autoplay-delay="3000"
  autoplay-disable-on-interaction="true"
  mousewheel="true"
  effect="fade"
>
  {
    images.map((item: { src: ImageMetadata | string; alt: string }) => (
      <swiper-slide>
        {typeof item.src === 'string' ? (
          <img
            src={item.src}
            alt={item.alt}
            width={imageProps.width || 1200}
            height={imageProps.height || 675}
            class="aspect-[16/9] object-cover"
          />
        ) : (
          <Image
            src={item.src as ImageMetadata}
            alt={item.alt}
            width={imageProps.width || 1200}
            height={imageProps.height || 675}
            quality={imageProps.quality}
            class="aspect-[16/9] object-cover"
          />
        )}
      </swiper-slide>
    ))
  }
</swiper-container>

<script>
  import { register } from 'swiper/element/bundle'
  import 'swiper/css'
  import 'swiper/css/autoplay'
  import 'swiper/css/effect-fade'
  import 'swiper/css/pagination'

  register()
</script>

---
// TransferAuditLog.astro
// Component for tracking and displaying data transfer audit logs
import { Icon } from 'astro-icon/components'

interface Props {
  title?: string
}

const { title = 'Data Transfer Audit Log' } = Astro.props

// Sample audit log entries for demonstration
const auditLogEntries = [
  {
    id: 'log-001',
    timestamp: '2025-07-15T14:32:45Z',
    patientId: process.env['PATIENT_ID'] || 'example-patient-id',
    initiatedBy: 'Dr. <PERSON>',
    recipientType: 'Healthcare Provider',
    recipientDetails: 'Northwest Medical Center',
    dataFormat: 'FHIR',
    sections: ['Patient Profile', 'Mental Health Data'],
    status: 'completed',
    encryptionVerified: true,
  },
  {
    id: 'log-002',
    timestamp: '2025-07-14T09:15:21Z',
    patientId: process.env['PATIENT_ID'] || 'example-patient-id',
    initiatedBy: 'Dr. <PERSON>',
    recipientType: 'Patient Direct Access',
    recipientDetails: 'Patient Email',
    dataFormat: 'JSON',
    sections: ['Patient Profile', 'Chat History', 'Consent Records'],
    status: 'completed',
    encryptionVerified: true,
  },
  {
    id: 'log-003',
    timestamp: '2025-07-13T16:45:09Z',
    patientId: process.env['PATIENT_ID'] || 'example-patient-id',
    initiatedBy: 'Dr. Lisa Chen',
    recipientType: 'Research Institution',
    recipientDetails: 'University Medical Research',
    dataFormat: 'CSV',
    sections: ['Mental Health Data'],
    status: 'completed',
    encryptionVerified: true,
  },
  {
    id: 'log-004',
    timestamp: '2025-07-12T11:23:54Z',
    patientId: process.env['PATIENT_ID'] || 'example-patient-id',
    initiatedBy: 'Dr. Sarah Johnson',
    recipientType: 'Healthcare Provider',
    recipientDetails: 'Eastside Clinic',
    dataFormat: 'CCD',
    sections: ['Patient Profile', 'Mental Health Data', 'Chat History'],
    status: 'failed',
    encryptionVerified: false,
    failureReason: 'Encryption verification failed',
  },
  {
    id: 'log-005',
    timestamp: '2025-07-10T08:12:37Z',
    patientId: process.env['PATIENT_ID'] || 'example-patient-id',
    initiatedBy: 'System Administrator',
    recipientType: 'Patient Direct Access',
    recipientDetails: 'Patient Portal',
    dataFormat: 'JSON',
    sections: ['Patient Profile', 'Consent Records'],
    status: 'completed',
    encryptionVerified: true,
  },
]

// Format date for display
const formatDate = (dateString: string | number | Date) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

// Get status class for color coding
const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30'
    case 'failed':
      return 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
    case 'in-progress':
      return 'text-blue-700 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/30'
    default:
      return 'text-gray-700 bg-gray-100 dark:text-gray-400 dark:bg-gray-800'
  }
}

// Get encryption verification class
const getEncryptionClass = (verified: boolean) => {
  return verified
    ? 'text-green-700 dark:text-green-400'
    : 'text-red-700 dark:text-red-400'
}
---

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
      {title}
    </h2>

    <div class="mb-8">
      <p class="text-gray-600 dark:text-gray-300 mb-4">
        Complete audit trail of all data transfers, including timestamps,
        recipient information, and encryption verification. All data transfer
        activities are logged for compliance with HIPAA regulations.
      </p>

      <!-- Filter and Search Controls -->
      <div class="flex flex-col md:flex-row gap-4 mb-6">
        <div class="flex-1">
          <label for="search" class="sr-only">Search</label>
          <div class="relative">
            <div
              class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
            >
              <Icon
                name="search"
                class="w-5 h-5 text-gray-500 dark:text-gray-400"
              />
            </div>
            <input
              type="search"
              id="search"
              class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
              placeholder="Search by patient ID, provider..."
            />
          </div>
        </div>

        <div class="w-full md:w-auto flex flex-col md:flex-row gap-2">
          <select
            id="status-filter"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
          >
            <option value="">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="in-progress">In Progress</option>
          </select>

          <select
            id="date-filter"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
          >
            <option value="7">Last 7 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="all">All Time</option>
          </select>

          <button
            type="button"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Audit Log Table -->
    <div class="overflow-x-auto relative shadow-md sm:rounded-lg">
      <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead
          class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400"
        >
          <tr>
            <th scope="col" class="py-3 px-4">Timestamp</th>
            <th scope="col" class="py-3 px-4">Patient ID</th>
            <th scope="col" class="py-3 px-4">Initiated By</th>
            <th scope="col" class="py-3 px-4">Recipient</th>
            <th scope="col" class="py-3 px-4">Format</th>
            <th scope="col" class="py-3 px-4">Status</th>
            <th scope="col" class="py-3 px-4">Encryption</th>
            <th scope="col" class="py-3 px-4">Actions</th>
          </tr>
        </thead>
        <tbody>
          {
            auditLogEntries.map((entry) => (
              <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="py-3 px-4 font-medium text-gray-900 dark:text-white whitespace-nowrap">
                  {formatDate(entry.timestamp)}
                </td>
                <td class="py-3 px-4">{entry.patientId}</td>
                <td class="py-3 px-4">{entry.initiatedBy}</td>
                <td class="py-3 px-4">
                  <div>
                    <div class="font-medium">{entry.recipientType}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      {entry.recipientDetails}
                    </div>
                  </div>
                </td>
                <td class="py-3 px-4">{entry.dataFormat}</td>
                <td class="py-3 px-4">
                  <span
                    class={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(entry.status)}`}
                  >
                    {entry.status.charAt(0).toUpperCase() +
                      entry.status.slice(1)}
                  </span>
                </td>
                <td class="py-3 px-4">
                  <div
                    class={`flex items-center ${getEncryptionClass(entry.encryptionVerified)}`}
                  >
                    {entry.encryptionVerified ? (
                      <span class="flex items-center">
                        <Icon name="check-circle" class="w-4 h-4 mr-1" />
                        Verified
                      </span>
                    ) : (
                      <span class="flex items-center">
                        <Icon name="x-circle" class="w-4 h-4 mr-1" />
                        Failed
                      </span>
                    )}
                  </div>
                </td>
                <td class="py-3 px-4">
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      class="font-medium text-blue-600 dark:text-blue-500 hover:underline"
                      aria-label="View details"
                    >
                      <Icon name="eye" class="w-5 h-5" />
                    </button>
                    <button
                      type="button"
                      class="font-medium text-gray-600 dark:text-gray-500 hover:underline"
                      aria-label="Download report"
                    >
                      <Icon name="download" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))
          }
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="flex justify-between items-center pt-4 px-2">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          Showing <span class="font-medium">1</span> to <span
            class="font-medium">5</span
          > of <span class="font-medium">25</span> entries
        </div>
        <div class="flex space-x-1">
          <button
            type="button"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            Previous
          </button>
          <button
            type="button"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-blue-50 dark:border-gray-600 dark:text-white dark:bg-blue-900/30"
          >
            1
          </button>
          <button
            type="button"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            2
          </button>
          <button
            type="button"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Initialize filters and search functionality
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('search')
    const statusFilter = document.getElementById('status-filter')
    const dateFilter = document.getElementById('date-filter')

    // In a real implementation, these would filter the table data
    // For demonstration purposes, we'll just log the filter changes

    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = (e.target as HTMLInputElement).value
        console.log('Search term:', searchTerm)
      })
    }

    if (statusFilter) {
      statusFilter.addEventListener('change', (e) => {
        const status = (e.target as HTMLSelectElement).value
        console.log('Status filter:', status)
      })
    }

    if (dateFilter) {
      dateFilter.addEventListener('change', (e) => {
        const days = (e.target as HTMLSelectElement).value
        console.log('Date filter:', days)
      })
    }
  })
</script>
